<template>
  <div class="pump-room-card" :class="{ inspected: isInspected }" @click="handleClick">
    <!-- 核查状态标识 -->
    <div class="inspection-badge" v-if="isInspected">
      <wd-icon name="check-circle" size="14px" color="#fff" />
      <span>已核查</span>
    </div>

    <!-- 卡片头部 -->
    <div class="card-header">
      <div class="pump-room-info">
        <div class="pump-room-name">{{ item.PumpHouseName || item.name || '泵房' }}</div>
        <div class="pump-room-subtitle" v-if="item.address || item.location">
          <wd-icon name="location" size="12px" color="#666" />
          <span>{{ item.address || item.location }}</span>
        </div>
      </div>
      <div class="header-actions">
        <!-- 导航按钮 -->
        <div @click.stop="openNavigation" class="navigation-button" :class="{ loading: navigationLoading, disabled: !hasCoordinates }" v-if="hasCoordinates">
          <div class="nav-icon-wrapper">
            <wd-icon v-if="!navigationLoading" name="location" size="14px" color="#fff"></wd-icon>
            <div v-else class="loading-spinner"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 泵房详情 -->
    <div class="pump-room-details" v-if="isInspected">
      <!-- 已核查状态显示核查信息 -->
      <div class="detail-row">
        <div class="detail-label">
          <wd-icon name="calendar" size="14px" color="#059669" />
          <span>核查时间</span>
        </div>
        <div class="detail-value inspection-info">{{ formatTime(item.InspectionTime) }}</div>
      </div>

      <div class="detail-row">
        <div class="detail-label">
          <wd-icon name="user" size="14px" color="#059669" />
          <span>核查人员</span>
        </div>
        <div class="detail-value inspection-info">{{ item.InspectionBy }}</div>
      </div>
    </div>
    <!-- 卡片底部 -->
    <div class="card-footer">
      <div class="action-hint">
        <span>点击查看详情</span>
      </div>
      <div class="card-arrow">
        <wd-icon name="arrow-right" size="16px" color="#999" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useToast } from 'wot-design-uni'
import * as CommonApi from '@/services/model/common.js'
import { cache } from '@/utils/cache.js'

const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  index: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['click'])

const toast = useToast()
const navigationLoading = ref(false)
const isWechatMiniProgram = ref(false)

// 检测是否为微信小程序环境
try {
  isWechatMiniProgram.value = typeof wx !== 'undefined' && wx.getSystemInfoSync
} catch (e) {
  isWechatMiniProgram.value = false
}

// 计算属性
const isInspected = computed(() => {
  return props.item.IsInspected === 1 || props.item.IsInspected === '1'
})

const hasCoordinates = computed(() => {
  return props.item.X && props.item.Y
})

// 格式化时间
function formatTime(time) {
  if (!time) return ''

  const date = new Date(time)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) {
    return '刚刚'
  } else if (diff < 3600000) {
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) {
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}

// 获取状态样式类
function getStatusClass(item) {
  const status = item.status || item.ProgressStatus || ''
  if (status.includes('正常') || status.includes('运行')) {
    return 'status-normal'
  } else if (status.includes('维护') || status.includes('检修')) {
    return 'status-maintenance'
  } else if (status.includes('异常') || status.includes('故障')) {
    return 'status-error'
  }
  return 'status-normal'
}

// 导航功能
async function openNavigation() {
  uni.vibrateShort({ type: 'medium' })
  try {
    // 检查是否有坐标信息
    if (!hasCoordinates.value) {
      return toast.warning('暂无坐标信息')
    }

    // 防止重复点击
    if (navigationLoading.value) {
      return
    }

    // 设置加载状态
    navigationLoading.value = true

    // 显示加载提示
    uni.showLoading({
      title: '正在获取位置...',
      mask: true
    })

    const originalLng = props.item.X
    const originalLat = props.item.Y

    // 验证坐标有效性
    if (isNaN(originalLng) || isNaN(originalLat)) {
      uni.hideLoading()
      return toast.error('坐标信息格式错误')
    }

    // 转换成高德坐标（GCJ02坐标系）
    const { data } = await CommonApi.coordinateTransformation({
      lng: originalLng,
      lat: originalLat,
      fn: 'wgs2gcj'
    })

    // 计算转换后的坐标
    const lng = Number(originalLng) + Number(data.lng.replace(originalLng, ''))
    const lat = Number(originalLat) + Number(data.lat.replace(originalLat, ''))

    uni.hideLoading()

    // 检测是否为微信小程序环境
    if (isWechatMiniProgram.value) {
      // 使用微信小程序原生导航API
      wx.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 16, // 地图缩放级别，范围5-18
        name: props.item.PumpHouseName || '泵房位置',
        address: `${props.item.BelongingStreet || ''}${props.item.ResidentialAddress || props.item.address || props.item.location || ''}`,
        success: () => {
          navigationLoading.value = false
        },
        fail: (error) => {
          console.error('导航失败:', error)
          toast.error('导航失败，请稍后重试')
          navigationLoading.value = false
        }
      })
    } else {
      // 非微信小程序环境，使用uni-app的openLocation
      uni.openLocation({
        latitude: lat,
        longitude: lng,
        scale: 16,
        name: props.item.PumpHouseName || '泵房位置',
        address: `${props.item.BelongingStreet || ''}${props.item.ResidentialAddress || props.item.address || props.item.location || ''}`,
        success: () => {
          navigationLoading.value = false
        },
        fail: (error) => {
          console.error('导航失败:', error)
          toast.error('导航失败，请稍后重试')
          navigationLoading.value = false
        }
      })
    }
  } catch (error) {
    uni.hideLoading()
    navigationLoading.value = false
    console.error('openNavigation 执行失败:', error)

    // 根据错误类型给出不同的提示
    if (error.message && error.message.includes('网络')) {
      toast.error('网络连接失败，请检查网络后重试')
    } else if (error.message && error.message.includes('坐标转换')) {
      toast.error('坐标转换失败，请稍后重试')
    } else {
      toast.error('导航功能暂时不可用，请稍后重试')
    }
  }
}

// 点击事件
function handleClick() {
  emit('click', props.item)
}
</script>

<style lang="less" scoped>
.pump-room-card {
  background: rgba(255, 255, 255, 0.98);
  border-radius: 10rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  border: 1rpx solid rgba(255, 255, 255, 0.9);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  backdrop-filter: blur(8rpx);

  // 已核查状态样式
  &.inspected {
    background: rgba(236, 253, 245, 0.98);
    border: 2rpx solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.15);

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4rpx;
      background: linear-gradient(90deg, #10b981 0%, #059669 100%);
      z-index: 3;
    }
  }

  // 添加卡片光泽效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
    transition: left 0.5s ease;
    z-index: 1;
    pointer-events: none;
  }

  &:active {
    transform: translateY(2rpx) scale(0.99);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.15);

    &::before {
      left: 100%;
    }
  }

  &:hover::before {
    left: 100%;
  }

  animation: slideInUp 0.4s ease-out;
}

// 核查状态标识
.inspection-badge {
  position: absolute;
  top: 16rpx;
  right: 80rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  padding: 8rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: 600;
  z-index: 4;
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.3);
  animation: badge-bounce 0.6s ease-out;
}

@keyframes badge-bounce {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(-10rpx);
  }
  60% {
    opacity: 1;
    transform: scale(1.1) translateY(0);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 卡片头部
.card-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 20rpx 20rpx 16rpx;
  position: relative;
  z-index: 2;
}

.pump-room-info {
  flex: 1;
  margin-right: 16rpx;
}

.pump-room-name {
  font-size: 30rpx;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
  margin-bottom: 8rpx;
}

.pump-room-subtitle {
  display: flex;
  align-items: center;
  gap: 6rpx;
  font-size: 22rpx;
  color: #666;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.card-index {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
  color: white;
  border-radius: 50%;
  font-size: 20rpx;
  font-weight: 700;
  box-shadow: 0 4rpx 12rpx rgba(66, 153, 225, 0.3);
}

// 导航按钮
.navigation-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-radius: 50%;
  box-shadow: 0 4rpx 12rpx rgba(245, 158, 11, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(245, 158, 11, 0.4);
  }

  &.loading {
    background: linear-gradient(135deg, #9ca3af 0%, #6b7280 100%);
    box-shadow: 0 4rpx 12rpx rgba(156, 163, 175, 0.3);
  }

  &.disabled {
    background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
    box-shadow: 0 2rpx 8rpx rgba(209, 213, 219, 0.2);
    cursor: not-allowed;
  }

  // 添加按钮光泽效果
  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 1;
  }
}

.nav-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
}

// 加载动画
.loading-spinner {
  width: 14px;
  height: 14px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid #fff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 泵房详情
.pump-room-details {
  padding: 16rpx 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);

  position: relative;
  z-index: 2;
}

.detail-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 6rpx;
  min-width: 120rpx;
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 500;
}

.detail-value {
  // flex: 1;
  margin-left: 24rpx;
  font-size: 22rpx;
  color: #374151;
  line-height: 1.4;
  word-break: break-all;

  &.status-normal {
    color: #059669;
    font-weight: 600;
  }

  &.status-maintenance {
    color: #d97706;
    font-weight: 600;
  }

  &.status-error {
    color: #dc2626;
    font-weight: 600;
  }

  &.inspection-info {
    color: #059669;
    font-weight: 600;
    background: rgba(16, 185, 129, 0.1);
    padding: 4rpx 8rpx;
    border-radius: 8rpx;
    border: 1rpx solid rgba(16, 185, 129, 0.2);
  }
}

// 卡片底部
.card-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12rpx 20rpx;
  background: rgba(248, 250, 252, 0.5);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 2;
}

.action-hint {
  font-size: 20rpx;
  color: #9ca3af;
  font-weight: 500;
}

.card-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28rpx;
  height: 28rpx;
  background: rgba(156, 163, 175, 0.1);
  border-radius: 50%;
  transition: all 0.3s ease;
}

// 响应式设计
@media (max-width: 750rpx) {
  .inspection-badge {
    top: 12rpx;
    right: 12rpx;
    padding: 6rpx 10rpx;
    font-size: 18rpx;
  }

  .card-header {
    padding: 16rpx 16rpx 12rpx;
  }

  .pump-room-name {
    font-size: 26rpx;
  }

  .pump-room-subtitle {
    font-size: 20rpx;
  }

  .header-actions {
    gap: 8rpx;
  }

  .card-index,
  .navigation-button {
    width: 40rpx;
    height: 40rpx;
    font-size: 18rpx;
  }

  .loading-spinner {
    width: 12px;
    height: 12px;
  }

  .pump-room-details {
    padding: 12rpx 16rpx;
  }

  .detail-label {
    min-width: 100rpx;
    font-size: 20rpx;
  }

  .detail-value {
    font-size: 20rpx;
  }

  .card-footer {
    padding: 10rpx 16rpx;
  }

  .action-hint {
    font-size: 18rpx;
  }
}
</style>
