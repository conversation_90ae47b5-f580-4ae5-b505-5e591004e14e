<template>
  <div class="pump-house-inspect">
    <!-- 物业信息表单 -->
    <view class="form-section property-section">
      <view class="section-header">
        <view class="section-icon property-icon">
          <wd-icon name="home" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">基础信息</text>
          <text class="title-subtitle">需核查的数据项</text>
        </view>
      </view>

      <view class="form-content">
        <wd-cell-group custom-class="form-group" border>
          <wd-input label="泵房名称" label-width="220rpx" show-word-limit prop="PumpHouseName" suffix-icon="warn-bold" clearable v-model="updataData.PumpHouseName" v-if="isExist('PumpHouseName')" placeholder="请输入泵房名称" />
          <wd-picker label="改造状态" placeholder="选择状态" label-width="220rpx" prop="RemouldState" v-model="updataData.RemouldState" v-if="isExist('RemouldState')" :columns="selectOption.GZ" />
          <wd-picker label="泵房批次" placeholder="选择批次" label-width="220rpx" prop="Batch" v-model="updataData.Batch" v-if="isExist('Batch')" :columns="selectOption.LX" />
          <wd-input label="小区地址" label-width="220rpx" show-word-limit prop="ResidentialAddress" suffix-icon="warn-bold" v-if="isExist('ResidentialAddress')" clearable v-model="updataData.ResidentialAddress" placeholder="请输入地址" />
          <wd-input
            label="加压供水户数"
            type="number"
            label-width="220rpx"
            show-word-limit
            prop="PressurizedHouseholds"
            v-if="isExist('PressurizedHouseholds')"
            suffix-icon="warn-bold"
            clearable
            v-model="updataData.PressurizedHouseholds"
            placeholder="请输入加压户数"
          />
          <wd-input label="小区建设时间" label-width="220rpx" show-word-limit prop="ConstructionTime" v-if="isExist('ConstructionTime')" suffix-icon="warn-bold" clearable v-model="updataData.ConstructionTime" placeholder="请输入建设时间" />
          <wd-input
            label="泵房管理状态"
            label-width="220rpx"
            show-word-limit
            prop="PumpRoomControlledState"
            v-if="isExist('PumpRoomControlledState')"
            suffix-icon="warn-bold"
            clearable
            v-model="updataData.PumpRoomControlledState"
            placeholder="请输入管理状态"
          />
          <wd-input label="物业单位" label-width="220rpx" show-word-limit prop="PropertyUnit" v-if="isExist('PropertyUnit')" suffix-icon="warn-bold" clearable v-model="updataData.PropertyUnit" placeholder="请输入物业管理单位" />
          <wd-input label="物业联系人" label-width="220rpx" show-word-limit prop="ContactPerson" v-if="isExist('ContactPerson')" suffix-icon="warn-bold" clearable v-model="updataData.ContactPerson" placeholder="请输入联系人" />
          <wd-input label="物业电话" label-width="220rpx" show-word-limit prop="PhoneNumber" v-if="isExist('PhoneNumber')" suffix-icon="warn-bold" clearable v-model="updataData.PhoneNumber" placeholder="请输入电话" />
          <wd-picker label="项目进展状态" placeholder="选择进展状态" label-width="250rpx" prop="ProgressStatus" v-if="isExist('ProgressStatus')" v-model="updataData.ProgressStatus" :columns="selectOption.ZT" />
          <wd-picker label="运营管理状态" placeholder="选择管理状态" label-width="220rpx" prop="Batch" v-if="isExist('OperationManagementState')" v-model="updataData.OperationManagementState" :columns="selectOption.YY" />
          <wd-input label="施工单位" label-width="250rpx" show-word-limit prop="ConstructionUnit" v-if="isExist('ConstructionUnit')" suffix-icon="warn-bold" clearable v-model="updataData.ConstructionUnit" placeholder="请输入施工单位" />
          <wd-input label="现场监管责任人" label-width="250rpx" show-word-limit prop="PersonInCharge" v-if="isExist('PersonInCharge')" suffix-icon="warn-bold" clearable v-model="updataData.PersonInCharge" placeholder="请输入责任人" />
          <wd-input
            label="临供停水事件数"
            label-width="250rpx"
            show-word-limit
            prop="TemporarySupplyEvents"
            v-if="isExist('TemporarySupplyEvents')"
            suffix-icon="warn-bold"
            clearable
            v-model="updataData.TemporarySupplyEvents"
            placeholder="请输入停水数"
          />
          <wd-calendar label="初步验收时间" label-width="250rpx" custom-class=" " v-model="updataData.AcceptanceTime" prop="AcceptanceTime" v-if="isExist('AcceptanceTime')" />
          <wd-textarea label="备注" auto-height type="textarea" v-model="updataData.Remark" :maxlength="200" show-word-limit placeholder="请输入备注信息" clearable prop="Remark" v-if="isExist('Remark')" />
          <wd-textarea
            label="小区地址"
            auto-height
            type="textarea"
            v-model="updataData.ResidentialAddress"
            v-if="isExist('ResidentialAddress')"
            :maxlength="200"
            show-word-limit
            placeholder="请输入地址信息"
            clearable
            prop="ResidentialAddress"
          />
          <wd-textarea
            label="泵房精确位置"
            auto-height
            type="textarea"
            v-model="updataData.AccuratePosition"
            v-if="isExist('AccuratePosition')"
            show-word-limit
            placeholder="泵房精确位置填写规则 如：xxx小区xxx栋地下二层"
            clearable
            prop="AccuratePosition"
          />
          <view class="image-upload-section" v-if="isExist('PumpHouseImg')">
            <view class="upload-title">泵房图片</view>
            <view class="upload-container">
              <UploadeImg :maxlength="3" :handleImg="({ data }) => data" :url="url" v-model="updataData.PumpHouseImg" />
            </view>
          </view>
        </wd-cell-group>
      </view>
    </view>

    <view class="form-section basic-section">
      <view class="section-header">
        <view class="section-icon basic-icon">
          <wd-icon name="setting" size="32rpx" color="#ffffff" />
        </view>
        <view class="section-title">
          <text class="title-text">节点信息</text>
          <text class="title-subtitle">泵房项目节点状态设置</text>
        </view>
      </view>

      <view class="form-content">
        <view class="node-cards-container">
          <template v-for="item in updataPumpHouseNodeData" :key="item.nodeCode">
            <view class="node-card" :class="{ 'node-completed': item.nodeData?.IsEnd, 'node-empty': !item.nodeData }">
              <!-- 卡片头部 -->
              <view class="node-card-header">
                <view class="node-info">
                  <view class="node-number">{{ item.nodeCode }}</view>
                  <view class="node-title">{{ item.nodeName }}</view>
                </view>
                <view class="node-status" :class="getNodeStatusClass(item)">
                  <wd-icon :name="getNodeStatusIcon(item)" size="16px" :color="getNodeStatusColor(item)" />
                  <text class="status-text">{{ getNodeStatusText(item) }}</text>
                </view>
              </view>

              <!-- 卡片内容 - 仅当有nodeData时显示 -->
              <view class="node-card-content" v-if="item.nodeData">
                <!-- 完成状态开关 -->
                <view class="node-field">
                  <view class="field-label">
                    <wd-icon name="check-circle" size="14px" color="#666" />
                    <text>完成状态</text>
                  </view>
                  <wd-switch v-model="item.nodeData.IsEnd" size="20px" @change="handleNodeStatusChange(item)" />
                </view>

                <!-- 完成时间 -->
                <view class="node-field">
                  <view class="field-label">
                    <wd-icon name="calendar" size="14px" color="#666" />
                    <text>完成时间</text>
                  </view>
                  <wd-calendar v-model="item.nodeData.CompletionTime" custom-class="node-calendar" @confirm="handleDateChange(item)" :disabled="!item.nodeData.IsEnd">
                    <view class="date-display" :class="{ 'date-disabled': !item.nodeData.IsEnd }">
                      {{ formatDate(item.nodeData.CompletionTime) || '请选择日期' }}
                    </view>
                  </wd-calendar>
                </view>

                <!-- 备注信息 -->
                <view class="node-field">
                  <view class="field-label">
                    <wd-icon name="edit" size="14px" color="#666" />
                    <text>备注信息</text>
                  </view>
                  <wd-textarea v-model="item.nodeData.Remark" placeholder="请输入备注信息" :maxlength="200" show-word-limit auto-height :disabled="!item.nodeData.IsEnd" @change="handleRemarkChange(item)" />
                </view>

                <!-- 更新信息 -->
                <view class="node-meta" v-if="item.nodeData.UpdateTime">
                  <view class="meta-item">
                    <wd-icon name="time" size="12px" color="#999" />
                    <text class="meta-text">更新时间：{{ formatDateTime(item.nodeData.UpdateTime) }}</text>
                  </view>
                  <view class="meta-item" v-if="item.nodeData.UpdatePerson">
                    <wd-icon name="user" size="12px" color="#999" />
                    <text class="meta-text">更新人：{{ item.nodeData.UpdatePerson }}</text>
                  </view>
                </view>
              </view>

              <!-- 空状态提示 -->
              <view class="node-empty-state" v-else>
                <wd-icon name="info" size="24px" color="#d9d9d9" />
                <text class="empty-text">暂无数据</text>
              </view>
            </view>
          </template>
        </view>
      </view>
    </view>
  </div>
  <wd-toast />
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { onLoad } from '@dcloudio/uni-app'
import UploadeImg from '@/components/UploadeImg/index.vue'
import * as CommonApi from '@/services/model/common.js'
import { PumpHouseApi } from '@/services/model/pump.house'
import { getChangedObjects } from '@/utils/util.js'
import { useToast } from 'wot-design-uni'

const updataData = ref(null)
const updataPumpHouseNodeData = ref(null)
const toast = useToast()

onLoad(async ({ pumpRoomNumber, TaskID }) => {
  await getDetail(pumpRoomNumber, TaskID)
  getQueryDictionaries()
})

const url = computed(() => `https://www.szwgft.cn/nodeServer/resource/upload/pump_house?url=${pumpHouseDetail.value.PumpRoomNumber}/img`)
const TaskContent = computed(() => taskInfo.value?.TaskContent?.split(','))

const taskInfo = ref({})
const pumpHouseDetail = ref(null)
const pumpHouseNode = ref(null)
async function getDetail(pumpRoomNumber, TaskID) {
  toast.loading('正在加载...')
  try {
    const res = await Promise.all([PumpHouseApi.detail(pumpRoomNumber), PumpHouseApi.nodelist(pumpRoomNumber), PumpHouseApi.tackDetaul(TaskID)])
    pumpHouseDetail.value = res[0].data
    updataData.value = { ...res[0].data }
    pumpHouseNode.value = res[1].data
    updataPumpHouseNodeData.value = JSON.parse(JSON.stringify(res[1].data))
    console.log(res[1].data)

    taskInfo.value = res[2].data
    toast.close()
  } catch (error) {
    toast.close()
    console.log(error)

    toast.error('数据加载失败')
  }
}

const selectOption = ref({})
async function getQueryDictionaries() {
  const renderToStream = await Promise.all([queryDictionaries('pumpHouseProjectStatus'), queryDictionaries('pumpHouseProjectBatch'), queryDictionaries('pumpHouseOperationState'), queryDictionaries('pumpHouseRemouldState')])
  const [pumpHouseProjectStatus, pumpHouseProjectBatch, pumpHouseOperationState, pumpHouseRemouldState] = renderToStream
  selectOption.value.ZT = pumpHouseProjectStatus.map((item) => ({ label: item.DictValue, value: item.DictValue })) //项目进展状态
  selectOption.value.LX = pumpHouseProjectBatch.map((item) => ({ label: item.DictValue, value: item.DictCode })) //泵房批次
  selectOption.value.YY = pumpHouseOperationState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //运营管理状态
  selectOption.value.GZ = pumpHouseRemouldState.map((item) => ({ label: item.DictValue, value: item.DictValue })) //改造状态
}

async function queryDictionaries(type) {
  try {
    const { data } = await CommonApi.dictionaryLookup(type)
    return data
  } catch (error) {
    // message.error(error.message)
  }
}

function isExist(pr) {
  if (Array.isArray(pr)) {
    return pr.some((item) => TaskContent.value.includes(item))
  } else {
    return (TaskContent.value || []).includes(pr)
  }
}

// 节点状态相关方法
function getNodeStatusClass(item) {
  if (!item.nodeData) return 'status-empty'
  return item.nodeData.IsEnd ? 'status-completed' : 'status-pending'
}

function getNodeStatusIcon(item) {
  if (!item.nodeData) return 'info'
  return item.nodeData.IsEnd ? 'check-circle' : 'clock'
}

function getNodeStatusColor(item) {
  if (!item.nodeData) return '#d9d9d9'
  return item.nodeData.IsEnd ? '#52c41a' : '#faad14'
}

function getNodeStatusText(item) {
  if (!item.nodeData) return '暂无数据'
  return item.nodeData.IsEnd ? '已完成' : '进行中'
}

// 处理节点状态变更
function handleNodeStatusChange(item) {
  console.log('节点状态变更:', item.nodeName, item.nodeData.IsEnd)
  // 如果设置为未完成，清空完成时间
  if (!item.nodeData.IsEnd) {
    item.nodeData.CompletionTime = null
  } else {
    // 如果设置为完成且没有完成时间，设置为当前时间
    if (!item.nodeData.CompletionTime) {
      item.nodeData.CompletionTime = new Date().toISOString()
    }
  }
  // 这里可以添加保存到服务器的逻辑
  toast.success(`${item.nodeName} 状态已更新`)
}

// 处理日期变更
function handleDateChange(item) {
  console.log('日期变更:', item.nodeName, item.nodeData.CompletionTime)
  // 这里可以添加保存到服务器的逻辑
  toast.success(`${item.nodeName} 完成时间已更新`)
}

// 处理备注变更
function handleRemarkChange(item) {
  console.log('备注变更:', item.nodeName, item.nodeData.Remark)
  // 这里可以添加保存到服务器的逻辑
}

// 格式化日期显示
function formatDate(dateStr) {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return ''
  }
}

// 格式化日期时间显示
function formatDateTime(dateStr) {
  if (!dateStr) return ''
  try {
    const date = new Date(dateStr)
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return ''
  }
}
</script>

<style lang="less" scoped>
.pump-house-inspect {
  width: 100%;
  height: 100%;
  overflow-y: scroll;
}

/* 页面头部装饰 */
.page-header {
  position: relative;
  height: 120rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}

.header-decoration {
  position: relative;
  width: 100%;
  height: 100%;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(24, 144, 255, 0.1) 0%, rgba(64, 169, 255, 0.05) 100%);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 80rpx;
  height: 80rpx;
  top: 20rpx;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 60rpx;
  height: 60rpx;
  top: 10rpx;
  right: 20%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100rpx;
  height: 100rpx;
  top: 40rpx;
  right: 10%;
  animation-delay: 4s;
}

/* 表单分组样式 */
.form-section {
  // margin-bottom: 32rpx;
  background: #ffffff;
  // border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  animation: fadeInUp 0.6s ease-out;

  &.basic-section {
    animation-delay: 0.1s;
  }

  &.property-section {
    animation-delay: 0.2s;
  }

  &.project-section {
    animation-delay: 0.3s;
  }

  &.location-section {
    animation-delay: 0.4s;
  }

  &.node-section {
    animation-delay: 0.5s;
  }
}

/* 分组头部样式 */
.section-header {
  display: flex;
  align-items: center;
  padding: 32rpx 32rpx 24rpx;
  background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
  border-bottom: 1rpx solid #f0f0f0;
}

.section-icon {
  width: 72rpx;
  height: 72rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
}

.basic-icon {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
}

.property-icon {
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
}

.project-icon {
  background: linear-gradient(135deg, #fa8c16 0%, #ffa940 100%);
}

.location-icon {
  background: linear-gradient(135deg, #eb2f96 0%, #f759ab 100%);
}

.node-icon {
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
}

.section-title {
  flex: 1;
}

.title-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.title-subtitle {
  display: block;
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

/* 表单内容样式 */
.form-content {
  padding: 0;
}

:deep(.form-group) {
  border-radius: 0 !important;
  border: none !important;
  background: transparent !important;
  box-shadow: none !important;
}

/* 位置信息特殊样式 */
.location-update-btn {
  margin: 24rpx 32rpx;
  padding: 20rpx 32rpx;
  background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  box-shadow: 0 6rpx 20rpx rgba(82, 196, 26, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 3rpx 12rpx rgba(82, 196, 26, 0.4);
  }
}

.update-btn-text {
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 600;
}

.image-upload-section {
  padding: 32rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.upload-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 24rpx;
}

.upload-container {
  border-radius: 12rpx;
  overflow: hidden;
}

/* 节点信息现代化样式 */
.node-tabs-container {
  padding: 0;
}

:deep(.modern-node-tabs) {
  .wd-tabs__nav {
    background: #f8faff;
    padding: 16rpx 32rpx 0;
    border-bottom: 1rpx solid #f0f0f0;
  }

  .wd-tab {
    margin-right: 24rpx;
    margin-bottom: 16rpx;
    padding: 12rpx 20rpx;
    border-radius: 12rpx;
    background: #ffffff;
    border: 1rpx solid #e8e8e8;
    transition: all 0.3s ease;
    font-size: 28rpx;

    &.is-active {
      background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
      border-color: #722ed1;
      color: #ffffff;
      font-weight: 600;
    }
  }
}

.node-content {
  padding: 32rpx;
  min-height: 300rpx;
  background: #ffffff;
}

.node-detail {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

/* 状态区域 */
.status-section {
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4ff;
}

.status-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 16rpx;
}

:deep(.status-radio) {
  .wd-radio {
    margin-right: 16rpx;

    &.is-checked {
      background: #722ed1;
      border-color: #722ed1;
      color: #ffffff;
    }
  }
}

/* 时间和备注区域 */
.time-section,
.remark-section {
  margin-bottom: 8rpx;
}

/* 文件上传区域 */
.file-section {
  margin-top: 8rpx;
  padding: 24rpx;
  background: #f8faff;
  border-radius: 12rpx;
  border: 1rpx solid #e8f4ff;
}

.file-header {
  display: flex;
  align-items: center;
  gap: 12rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e0e6ff;
  margin-bottom: 16rpx;
}

.file-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333333;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16rpx 20rpx;
  background: #ffffff;
  border-radius: 12rpx;
  border: 1rpx solid #f0f0f0;
  transition: all 0.3s ease;
}

.file-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.file-name {
  font-size: 28rpx;
  color: #333333;
  font-weight: 500;

  &.file-completed {
    color: #52c41a;
  }
}

.file-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-text {
  font-size: 24rpx;
  color: #52c41a;
}

.file-action {
  margin-left: 16rpx;
}

.upload-btn {
  width: 60rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.95);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.4);
  }
}

/* 简化的上传按钮样式，确保app端兼容性 */
.upload-btn-simple {
  width: 80rpx;
  height: 80rpx;
  background-color: #722ed1;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 240rpx;
  padding: 40rpx 20rpx;
}

.empty-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20rpx;
  text-align: center;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  margin-bottom: 8rpx;
}

.add-btn {
  padding: 16rpx 32rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.2);
  transition: all 0.3s ease;

  &:active {
    transform: scale(0.98);
    box-shadow: 0 2rpx 8rpx rgba(114, 46, 209, 0.3);
  }
}

.add-text {
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 500;
}

/* 提交按钮区域样式 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.95) 20%, #ffffff 100%);
  padding: 24rpx 24rpx 40rpx;
  z-index: 10;
  backdrop-filter: blur(10rpx);
}

.submit-button-container {
  width: 100%;
  max-width: 600rpx;
  margin: 0 auto;
}

:deep(.submit-button) {
  width: 100% !important;
  height: 96rpx !important;
  border-radius: 24rpx !important;
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%) !important;
  border: none !important;
  box-shadow: 0 12rpx 32rpx rgba(24, 144, 255, 0.3) !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s ease;
  }

  &:active {
    transform: scale(0.98) !important;
    box-shadow: 0 8rpx 24rpx rgba(24, 144, 255, 0.4) !important;
  }

  &:not(:disabled):active::before {
    left: 100%;
  }

  &:disabled {
    background: linear-gradient(135deg, #d9d9d9 0%, #f0f0f0 100%) !important;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1) !important;
    transform: none !important;
  }
}

.button-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
}

.button-text {
  color: #ffffff;
  font-size: 32rpx;
  font-weight: 600;
}

.updateIcon {
  top: 16rpx;
  right: 26rpx;
  padding: 4rpx 18rpx;
  background: #15a131;
  border-radius: 12rpx;
}

:deep(.wd-tabs__map-nav-btn) {
  width: auto;
  height: auto;
  padding: 8rpx 18rpx;
  margin-bottom: 10rpx;
  line-height: normal;
}

/* 优化后的上传模态框样式 */
.upload-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: fadeIn 0.3s ease-out;
}

.upload-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4rpx);
}

.upload-modal-content {
  position: relative;
  background: #ffffff;
  border-radius: 32rpx;
  padding: 48rpx 40rpx;
  margin: 0 40rpx;
  max-width: 500rpx;
  width: 100%;
  box-shadow: 0 24rpx 64rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-header {
  text-align: center;
  margin-bottom: 40rpx;
}

.upload-icon {
  width: 96rpx;
  height: 96rpx;
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24rpx;
  animation: pulse 2s infinite;
}

.upload-subtitle {
  font-size: 28rpx;
  color: #666666;
  line-height: 1.4;
}

.upload-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.progress-container {
  width: 100%;
  position: relative;
}

.progress-text {
  text-align: center;
  font-size: 32rpx;
  font-weight: 600;
  color: #1890ff;
  margin-top: 16rpx;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(60rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* 动画效果 */
@keyframes float {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 节点卡片样式 */
.node-cards-container {
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.node-card {
  background: #ffffff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid #f0f0f0;
  overflow: hidden;
  transition: all 0.3s ease;
  margin: 0 24rpx;

  &:hover {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.12);
    transform: translateY(-2rpx);
  }

  &.node-completed {
    border-left: 6rpx solid #52c41a;
    background: linear-gradient(135deg, #f6ffed 0%, #ffffff 100%);
  }

  &.node-empty {
    border-left: 6rpx solid #d9d9d9;
    background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  }
}

.node-card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 32rpx 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: linear-gradient(135deg, #fafbff 0%, #ffffff 100%);
}

.node-info {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.node-number {
  width: 48rpx;
  height: 48rpx;
  background: linear-gradient(135deg, #722ed1 0%, #9254de 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-size: 24rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);
}

.node-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  line-height: 1.4;
}

.node-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;

  &.status-completed {
    background: rgba(82, 196, 26, 0.1);
    color: #52c41a;
  }

  &.status-pending {
    background: rgba(250, 173, 20, 0.1);
    color: #faad14;
  }

  &.status-empty {
    background: rgba(217, 217, 217, 0.1);
    color: #999999;
  }
}

.status-text {
  font-size: 24rpx;
}

.node-card-content {
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.node-field {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
}

.date-display {
  padding: 16rpx 20rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333333;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    border-color: #722ed1;
    background: #fafbff;
  }

  &.date-disabled {
    background: #f5f5f5;
    color: #999999;
    cursor: not-allowed;
    border-color: #d9d9d9;

    &:hover {
      border-color: #d9d9d9;
      background: #f5f5f5;
    }
  }
}

.node-meta {
  margin-top: 16rpx;
  padding-top: 16rpx;
  border-top: 1rpx solid #f0f0f0;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.meta-text {
  font-size: 24rpx;
  color: #999999;
}

.node-empty-state {
  padding: 60rpx 32rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

/* 自定义日历样式 */
:deep(.node-calendar) {
  .wd-calendar__input {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
  }
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .pump-house-update {
    padding: 16rpx;
  }

  .form-section {
    margin-bottom: 24rpx;
    border-radius: 20rpx;
  }

  .section-header {
    padding: 24rpx 24rpx 20rpx;
  }

  .section-icon {
    width: 64rpx;
    height: 64rpx;
    margin-right: 20rpx;
  }

  .title-text {
    font-size: 30rpx;
  }

  .title-subtitle {
    font-size: 22rpx;
  }

  .location-update-btn {
    margin: 20rpx 24rpx;
    padding: 16rpx 24rpx;
  }

  .image-upload-section {
    padding: 24rpx;
  }

  .node-content {
    padding: 24rpx;
  }

  .status-section {
    padding: 20rpx;
  }

  .file-section {
    padding: 20rpx;
  }

  .file-item {
    padding: 12rpx 16rpx;
  }

  .upload-btn {
    width: 56rpx;
    height: 56rpx;
  }

  .upload-btn-simple {
    width: 72rpx;
    height: 72rpx;
  }

  .upload-modal-content {
    margin: 0 24rpx;
    padding: 40rpx 32rpx;
  }

  .submit-section {
    padding: 20rpx 20rpx 32rpx;
  }

  .node-card {
    margin: 0 16rpx;
  }

  .node-card-header {
    padding: 20rpx 24rpx 16rpx;
  }

  .node-card-content {
    padding: 24rpx;
  }

  .node-number {
    width: 40rpx;
    height: 40rpx;
    font-size: 20rpx;
  }

  .node-title {
    font-size: 28rpx;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pump-house-update {
    background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
  }

  .form-section {
    background: #333333;
    border-color: #444444;
  }

  .section-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    border-bottom-color: #444444;
  }

  .title-text {
    color: #ffffff;
  }

  .title-subtitle {
    color: #cccccc;
  }

  .image-upload-section {
    background: #2a2a2a;
    border-top-color: #444444;
  }

  .node-content {
    background: #333333;
  }

  .status-section,
  .file-section {
    background: #2a2a2a;
    border-color: #444444;
  }

  .status-label,
  .file-title {
    color: #ffffff;
  }

  .file-header {
    border-bottom-color: #444444;
  }

  .file-item {
    background: #333333;
    border-color: #444444;
  }

  .file-name {
    color: #ffffff;

    &.file-completed {
      color: #52c41a;
    }
  }

  .empty-text {
    color: #cccccc;
  }

  /* 节点卡片深色模式 */
  .node-card {
    background: #333333;
    border-color: #444444;

    &.node-completed {
      background: linear-gradient(135deg, #1f2937 0%, #333333 100%);
    }

    &.node-empty {
      background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    }
  }

  .node-card-header {
    background: linear-gradient(135deg, #2a2a2a 0%, #333333 100%);
    border-bottom-color: #444444;
  }

  .node-title {
    color: #ffffff;
  }

  .field-label {
    color: #ffffff;
  }

  .date-display {
    background: #2a2a2a;
    border-color: #444444;
    color: #ffffff;

    &:hover {
      border-color: #722ed1;
      background: #1f2937;
    }

    &.date-disabled {
      background: #1a1a1a;
      color: #666666;
      border-color: #333333;

      &:hover {
        border-color: #333333;
        background: #1a1a1a;
      }
    }
  }

  .node-meta {
    border-top-color: #444444;
  }

  .meta-text {
    color: #cccccc;
  }

  .empty-text {
    color: #cccccc;
  }
}
</style>
