<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8" />
    <title>压力监测双折线图</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
      body {
        margin: 0;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      #chart {
        width: 100%;
        height: 600px;
      }
      .title {
        text-align: center;
        margin-bottom: 20px;
        color: #333;
      }
    </style>
  </head>
  <body>
    <h1 class="title">压力监测数据可视化</h1>
    <div id="chart"></div>

    <script>
      var chartDom = document.getElementById('chart')
      var myChart = echarts.init(chartDom)

      var option = {
        title: {
          text: '出口压力与末端压力对比',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function (params) {
            var result = params[0].axisValueLabel + '<br/>'
            params.forEach(function (item) {
              result += item.marker + item.seriesName + ': ' + item.value + (item.seriesName === '出口压力' ? ' MPa' : item.seriesName === '末端压力' ? ' MPa' : item.seriesName === '用水量' ? '吨' : ' Hz') + '<br/>'
            })
            return result
          }
        },
        legend: {
          data: ['出口压力', '末端压力', '一号泵频率', '二号泵频率', '用水量'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          containLabel: true
        },
        toolbox: {
          feature: {
            dataZoom: {
              yAxisIndex: 'none'
            },
            restore: {},
            saveAsImage: {}
          }
        },
        dataZoom: [
          {
            type: 'inside',
            start: 0,
            end: 100
          },
          {
            start: 0,
            end: 100,
            handleIcon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
            handleSize: '80%',
            handleStyle: {
              color: '#fff',
              shadowBlur: 3,
              shadowColor: 'rgba(0, 0, 0, 0.6)',
              shadowOffsetX: 2,
              shadowOffsetY: 2
            }
          }
        ],
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: [
            '2025-08-30 00:00:00',
            '2025-08-30 00:01:00',
            '2025-08-30 00:02:00',
            '2025-08-30 00:03:00',
            '2025-08-30 00:04:00',
            '2025-08-30 00:05:00',
            '2025-08-30 00:06:00',
            '2025-08-30 00:07:00',
            '2025-08-30 00:08:00',
            '2025-08-30 00:09:00',
            '2025-08-30 00:10:00',
            '2025-08-30 00:11:00',
            '2025-08-30 00:12:00',
            '2025-08-30 00:13:00',
            '2025-08-30 00:14:00',
            '2025-08-30 00:15:00',
            '2025-08-30 00:16:00',
            '2025-08-30 00:17:00',
            '2025-08-30 00:18:00',
            '2025-08-30 00:19:00',
            '2025-08-30 00:20:00',
            '2025-08-30 00:21:00',
            '2025-08-30 00:22:00',
            '2025-08-30 00:23:00',
            '2025-08-30 00:24:00',
            '2025-08-30 00:25:00',
            '2025-08-30 00:26:00',
            '2025-08-30 00:27:00',
            '2025-08-30 00:28:00',
            '2025-08-30 00:29:00',
            '2025-08-30 00:30:00',
            '2025-08-30 00:31:00',
            '2025-08-30 00:32:00',
            '2025-08-30 00:33:00',
            '2025-08-30 00:34:00',
            '2025-08-30 00:35:00',
            '2025-08-30 00:36:00',
            '2025-08-30 00:37:00',
            '2025-08-30 00:38:00',
            '2025-08-30 00:39:00',
            '2025-08-30 00:40:00',
            '2025-08-30 00:41:00',
            '2025-08-30 00:42:00',
            '2025-08-30 00:43:00',
            '2025-08-30 00:44:00',
            '2025-08-30 00:45:00',
            '2025-08-30 00:46:00',
            '2025-08-30 00:47:00',
            '2025-08-30 00:48:00',
            '2025-08-30 00:49:00',
            '2025-08-30 00:50:00',
            '2025-08-30 00:51:00',
            '2025-08-30 00:52:00',
            '2025-08-30 00:53:00',
            '2025-08-30 00:54:00',
            '2025-08-30 00:55:00',
            '2025-08-30 00:56:00',
            '2025-08-30 00:57:00',
            '2025-08-30 00:58:00',
            '2025-08-30 00:59:00',
            '2025-08-30 01:00:00',
            '2025-08-30 01:01:00',
            '2025-08-30 01:02:00',
            '2025-08-30 01:03:00',
            '2025-08-30 01:04:00',
            '2025-08-30 01:05:00',
            '2025-08-30 01:06:00',
            '2025-08-30 01:07:00',
            '2025-08-30 01:08:00',
            '2025-08-30 01:09:00',
            '2025-08-30 01:10:00',
            '2025-08-30 01:11:00',
            '2025-08-30 01:12:00',
            '2025-08-30 01:13:00',
            '2025-08-30 01:14:00',
            '2025-08-30 01:15:00',
            '2025-08-30 01:16:00',
            '2025-08-30 01:17:00',
            '2025-08-30 01:18:00',
            '2025-08-30 01:19:00',
            '2025-08-30 01:20:00',
            '2025-08-30 01:21:00',
            '2025-08-30 01:22:00',
            '2025-08-30 01:23:00',
            '2025-08-30 01:24:00',
            '2025-08-30 01:25:00',
            '2025-08-30 01:26:00',
            '2025-08-30 01:27:00',
            '2025-08-30 01:28:00',
            '2025-08-30 01:29:00',
            '2025-08-30 01:30:00',
            '2025-08-30 01:31:00',
            '2025-08-30 01:32:00',
            '2025-08-30 01:33:00',
            '2025-08-30 01:34:00',
            '2025-08-30 01:35:00',
            '2025-08-30 01:36:00',
            '2025-08-30 01:37:00',
            '2025-08-30 01:38:00',
            '2025-08-30 01:39:00',
            '2025-08-30 01:40:00',
            '2025-08-30 01:41:00',
            '2025-08-30 01:42:00',
            '2025-08-30 01:43:00',
            '2025-08-30 01:44:00',
            '2025-08-30 01:45:00',
            '2025-08-30 01:46:00',
            '2025-08-30 01:47:00',
            '2025-08-30 01:48:00',
            '2025-08-30 01:49:00',
            '2025-08-30 01:50:00',
            '2025-08-30 01:51:00',
            '2025-08-30 01:52:00',
            '2025-08-30 01:53:00',
            '2025-08-30 01:54:00',
            '2025-08-30 01:55:00',
            '2025-08-30 01:56:00',
            '2025-08-30 01:57:00',
            '2025-08-30 01:58:00',
            '2025-08-30 01:59:00',
            '2025-08-30 02:00:00',
            '2025-08-30 02:01:00',
            '2025-08-30 02:02:00',
            '2025-08-30 02:03:00',
            '2025-08-30 02:04:00',
            '2025-08-30 02:05:00',
            '2025-08-30 02:06:00',
            '2025-08-30 02:07:00',
            '2025-08-30 02:08:00',
            '2025-08-30 02:09:00',
            '2025-08-30 02:10:00',
            '2025-08-30 02:11:00',
            '2025-08-30 02:12:00',
            '2025-08-30 02:13:00',
            '2025-08-30 02:14:00',
            '2025-08-30 02:15:00',
            '2025-08-30 02:16:00',
            '2025-08-30 02:17:00',
            '2025-08-30 02:18:00',
            '2025-08-30 02:19:00',
            '2025-08-30 02:20:00',
            '2025-08-30 02:21:00',
            '2025-08-30 02:22:00',
            '2025-08-30 02:23:00',
            '2025-08-30 02:24:00',
            '2025-08-30 02:25:00',
            '2025-08-30 02:26:00',
            '2025-08-30 02:27:00',
            '2025-08-30 02:28:00',
            '2025-08-30 02:29:00',
            '2025-08-30 02:30:00',
            '2025-08-30 02:31:00',
            '2025-08-30 02:32:00',
            '2025-08-30 02:33:00',
            '2025-08-30 02:34:00',
            '2025-08-30 02:35:00',
            '2025-08-30 02:36:00',
            '2025-08-30 02:37:00',
            '2025-08-30 02:38:00',
            '2025-08-30 02:39:00',
            '2025-08-30 02:40:00',
            '2025-08-30 02:41:00',
            '2025-08-30 02:42:00',
            '2025-08-30 02:43:00',
            '2025-08-30 02:44:00',
            '2025-08-30 02:45:00',
            '2025-08-30 02:46:00',
            '2025-08-30 02:47:00',
            '2025-08-30 02:48:00',
            '2025-08-30 02:49:00',
            '2025-08-30 02:50:00',
            '2025-08-30 02:51:00',
            '2025-08-30 02:52:00',
            '2025-08-30 02:53:00',
            '2025-08-30 02:54:00',
            '2025-08-30 02:55:00',
            '2025-08-30 02:56:00',
            '2025-08-30 02:57:00',
            '2025-08-30 02:58:00',
            '2025-08-30 02:59:00',
            '2025-08-30 03:00:00',
            '2025-08-30 03:01:00',
            '2025-08-30 03:02:00',
            '2025-08-30 03:03:00',
            '2025-08-30 03:04:00',
            '2025-08-30 03:05:00',
            '2025-08-30 03:06:00',
            '2025-08-30 03:07:00',
            '2025-08-30 03:08:00',
            '2025-08-30 03:09:00',
            '2025-08-30 03:10:00',
            '2025-08-30 03:11:00',
            '2025-08-30 03:12:00',
            '2025-08-30 03:13:00',
            '2025-08-30 03:14:00',
            '2025-08-30 03:15:00',
            '2025-08-30 03:16:00',
            '2025-08-30 03:17:00',
            '2025-08-30 03:18:00',
            '2025-08-30 03:19:00',
            '2025-08-30 03:20:00',
            '2025-08-30 03:21:00',
            '2025-08-30 03:22:00',
            '2025-08-30 03:23:00',
            '2025-08-30 03:24:00',
            '2025-08-30 03:25:00',
            '2025-08-30 03:26:00',
            '2025-08-30 03:27:00',
            '2025-08-30 03:28:00',
            '2025-08-30 03:29:00',
            '2025-08-30 03:30:00',
            '2025-08-30 03:31:00',
            '2025-08-30 03:32:00',
            '2025-08-30 03:33:00',
            '2025-08-30 03:34:00',
            '2025-08-30 03:35:00',
            '2025-08-30 03:36:00',
            '2025-08-30 03:37:00',
            '2025-08-30 03:38:00',
            '2025-08-30 03:39:00',
            '2025-08-30 03:40:00',
            '2025-08-30 03:41:00',
            '2025-08-30 03:42:00',
            '2025-08-30 03:43:00',
            '2025-08-30 03:44:00',
            '2025-08-30 03:45:00',
            '2025-08-30 03:46:00',
            '2025-08-30 03:47:00',
            '2025-08-30 03:48:00',
            '2025-08-30 03:49:00',
            '2025-08-30 03:50:00',
            '2025-08-30 03:51:00',
            '2025-08-30 03:52:00',
            '2025-08-30 03:53:00',
            '2025-08-30 03:54:00',
            '2025-08-30 03:55:00',
            '2025-08-30 03:56:00',
            '2025-08-30 03:57:00',
            '2025-08-30 03:58:00',
            '2025-08-30 03:59:00',
            '2025-08-30 04:00:00',
            '2025-08-30 04:01:00',
            '2025-08-30 04:02:00',
            '2025-08-30 04:03:00',
            '2025-08-30 04:04:00',
            '2025-08-30 04:05:00',
            '2025-08-30 04:06:00',
            '2025-08-30 04:07:00',
            '2025-08-30 04:08:00',
            '2025-08-30 04:09:00',
            '2025-08-30 04:10:00',
            '2025-08-30 04:11:00',
            '2025-08-30 04:12:00',
            '2025-08-30 04:13:00',
            '2025-08-30 04:14:00',
            '2025-08-30 04:15:00',
            '2025-08-30 04:16:00',
            '2025-08-30 04:17:00',
            '2025-08-30 04:18:00',
            '2025-08-30 04:19:00',
            '2025-08-30 04:20:00',
            '2025-08-30 04:21:00',
            '2025-08-30 04:22:00',
            '2025-08-30 04:23:00',
            '2025-08-30 04:24:00',
            '2025-08-30 04:25:00',
            '2025-08-30 04:26:00',
            '2025-08-30 04:27:00',
            '2025-08-30 04:28:00',
            '2025-08-30 04:29:00',
            '2025-08-30 04:30:00',
            '2025-08-30 04:31:00',
            '2025-08-30 04:32:00',
            '2025-08-30 04:33:00',
            '2025-08-30 04:34:00',
            '2025-08-30 04:35:00',
            '2025-08-30 04:36:00',
            '2025-08-30 04:37:00',
            '2025-08-30 04:38:00',
            '2025-08-30 04:39:00',
            '2025-08-30 04:40:00',
            '2025-08-30 04:41:00',
            '2025-08-30 04:42:00',
            '2025-08-30 04:43:00',
            '2025-08-30 04:44:00',
            '2025-08-30 04:45:00',
            '2025-08-30 04:46:00',
            '2025-08-30 04:47:00',
            '2025-08-30 04:48:00',
            '2025-08-30 04:49:00',
            '2025-08-30 04:50:00',
            '2025-08-30 04:51:00',
            '2025-08-30 04:52:00',
            '2025-08-30 04:53:00',
            '2025-08-30 04:54:00',
            '2025-08-30 04:55:00',
            '2025-08-30 04:56:00',
            '2025-08-30 04:57:00',
            '2025-08-30 04:58:00',
            '2025-08-30 04:59:00',
            '2025-08-30 05:00:00',
            '2025-08-30 05:01:00',
            '2025-08-30 05:02:00',
            '2025-08-30 05:03:00',
            '2025-08-30 05:04:00',
            '2025-08-30 05:05:00',
            '2025-08-30 05:06:00',
            '2025-08-30 05:07:00',
            '2025-08-30 05:08:00',
            '2025-08-30 05:09:00',
            '2025-08-30 05:10:00',
            '2025-08-30 05:11:00',
            '2025-08-30 05:12:00',
            '2025-08-30 05:13:00',
            '2025-08-30 05:14:00',
            '2025-08-30 05:15:00',
            '2025-08-30 05:16:00',
            '2025-08-30 05:17:00',
            '2025-08-30 05:18:00',
            '2025-08-30 05:19:00',
            '2025-08-30 05:20:00',
            '2025-08-30 05:21:00',
            '2025-08-30 05:22:00',
            '2025-08-30 05:23:00',
            '2025-08-30 05:24:00',
            '2025-08-30 05:25:00',
            '2025-08-30 05:26:00',
            '2025-08-30 05:27:00',
            '2025-08-30 05:28:00',
            '2025-08-30 05:29:00',
            '2025-08-30 05:30:00',
            '2025-08-30 05:31:00',
            '2025-08-30 05:32:00',
            '2025-08-30 05:33:00',
            '2025-08-30 05:34:00',
            '2025-08-30 05:35:00',
            '2025-08-30 05:36:00',
            '2025-08-30 05:37:00',
            '2025-08-30 05:38:00',
            '2025-08-30 05:39:00',
            '2025-08-30 05:40:00',
            '2025-08-30 05:41:00',
            '2025-08-30 05:42:00',
            '2025-08-30 05:43:00',
            '2025-08-30 05:44:00',
            '2025-08-30 05:45:00',
            '2025-08-30 05:46:00',
            '2025-08-30 05:47:00',
            '2025-08-30 05:48:00',
            '2025-08-30 05:49:00',
            '2025-08-30 05:50:00',
            '2025-08-30 05:51:00',
            '2025-08-30 05:52:00',
            '2025-08-30 05:53:00',
            '2025-08-30 05:54:00',
            '2025-08-30 05:55:00',
            '2025-08-30 05:56:00',
            '2025-08-30 05:57:00',
            '2025-08-30 05:58:00',
            '2025-08-30 05:59:00',
            '2025-08-30 06:00:00',
            '2025-08-30 06:01:00',
            '2025-08-30 06:02:00',
            '2025-08-30 06:03:00',
            '2025-08-30 06:04:00',
            '2025-08-30 06:05:00',
            '2025-08-30 06:06:00',
            '2025-08-30 06:07:00',
            '2025-08-30 06:08:00',
            '2025-08-30 06:09:00',
            '2025-08-30 06:10:00',
            '2025-08-30 06:11:00',
            '2025-08-30 06:12:00',
            '2025-08-30 06:13:00',
            '2025-08-30 06:14:00',
            '2025-08-30 06:15:00',
            '2025-08-30 06:16:00',
            '2025-08-30 06:17:00',
            '2025-08-30 06:18:00',
            '2025-08-30 06:19:00',
            '2025-08-30 06:20:00',
            '2025-08-30 06:21:00',
            '2025-08-30 06:22:00',
            '2025-08-30 06:23:00',
            '2025-08-30 06:24:00',
            '2025-08-30 06:25:00',
            '2025-08-30 06:26:00',
            '2025-08-30 06:27:00',
            '2025-08-30 06:28:00',
            '2025-08-30 06:29:00',
            '2025-08-30 06:30:00',
            '2025-08-30 06:31:00',
            '2025-08-30 06:32:00',
            '2025-08-30 06:33:00',
            '2025-08-30 06:34:00',
            '2025-08-30 06:35:00',
            '2025-08-30 06:36:00',
            '2025-08-30 06:37:00',
            '2025-08-30 06:38:00',
            '2025-08-30 06:39:00',
            '2025-08-30 06:40:00',
            '2025-08-30 06:41:00',
            '2025-08-30 06:42:00',
            '2025-08-30 06:43:00',
            '2025-08-30 06:44:00',
            '2025-08-30 06:45:00',
            '2025-08-30 06:46:00',
            '2025-08-30 06:47:00',
            '2025-08-30 06:48:00',
            '2025-08-30 06:49:00',
            '2025-08-30 06:50:00',
            '2025-08-30 06:51:00',
            '2025-08-30 06:52:00',
            '2025-08-30 06:53:00',
            '2025-08-30 06:54:00',
            '2025-08-30 06:55:00',
            '2025-08-30 06:56:00',
            '2025-08-30 06:57:00',
            '2025-08-30 06:58:00',
            '2025-08-30 06:59:00',
            '2025-08-30 07:00:00',
            '2025-08-30 07:01:00',
            '2025-08-30 07:02:00',
            '2025-08-30 07:03:00',
            '2025-08-30 07:04:00',
            '2025-08-30 07:05:00',
            '2025-08-30 07:06:00',
            '2025-08-30 07:07:00',
            '2025-08-30 07:08:00',
            '2025-08-30 07:09:00',
            '2025-08-30 07:10:00',
            '2025-08-30 07:11:00',
            '2025-08-30 07:12:00',
            '2025-08-30 07:13:00',
            '2025-08-30 07:14:00',
            '2025-08-30 07:15:00',
            '2025-08-30 07:16:00',
            '2025-08-30 07:17:00',
            '2025-08-30 07:18:00',
            '2025-08-30 07:19:00',
            '2025-08-30 07:20:00',
            '2025-08-30 07:21:00',
            '2025-08-30 07:22:00',
            '2025-08-30 07:23:00',
            '2025-08-30 07:24:00',
            '2025-08-30 07:25:00',
            '2025-08-30 07:26:00',
            '2025-08-30 07:27:00',
            '2025-08-30 07:28:00',
            '2025-08-30 07:29:00',
            '2025-08-30 07:30:00',
            '2025-08-30 07:31:00',
            '2025-08-30 07:32:00',
            '2025-08-30 07:33:00',
            '2025-08-30 07:34:00',
            '2025-08-30 07:35:00',
            '2025-08-30 07:36:00',
            '2025-08-30 07:37:00',
            '2025-08-30 07:38:00',
            '2025-08-30 07:39:00',
            '2025-08-30 07:40:00',
            '2025-08-30 07:41:00',
            '2025-08-30 07:42:00',
            '2025-08-30 07:43:00',
            '2025-08-30 07:44:00',
            '2025-08-30 07:45:00',
            '2025-08-30 07:46:00',
            '2025-08-30 07:47:00',
            '2025-08-30 07:48:00',
            '2025-08-30 07:49:00',
            '2025-08-30 07:50:00',
            '2025-08-30 07:51:00',
            '2025-08-30 07:52:00',
            '2025-08-30 07:53:00',
            '2025-08-30 07:54:00',
            '2025-08-30 07:55:00',
            '2025-08-30 07:56:00',
            '2025-08-30 07:57:00',
            '2025-08-30 07:58:00',
            '2025-08-30 07:59:00',
            '2025-08-30 08:00:00',
            '2025-08-30 08:01:00',
            '2025-08-30 08:02:00',
            '2025-08-30 08:03:00',
            '2025-08-30 08:04:00',
            '2025-08-30 08:05:00',
            '2025-08-30 08:06:00',
            '2025-08-30 08:07:00',
            '2025-08-30 08:08:00',
            '2025-08-30 08:09:00',
            '2025-08-30 08:10:00',
            '2025-08-30 08:11:00',
            '2025-08-30 08:12:00',
            '2025-08-30 08:13:00',
            '2025-08-30 08:14:00',
            '2025-08-30 08:15:00',
            '2025-08-30 08:16:00',
            '2025-08-30 08:17:00',
            '2025-08-30 08:18:00',
            '2025-08-30 08:19:00',
            '2025-08-30 08:20:00',
            '2025-08-30 08:21:00',
            '2025-08-30 08:22:00',
            '2025-08-30 08:23:00',
            '2025-08-30 08:24:00',
            '2025-08-30 08:25:00',
            '2025-08-30 08:26:00',
            '2025-08-30 08:27:00',
            '2025-08-30 08:28:00',
            '2025-08-30 08:29:00',
            '2025-08-30 08:30:00',
            '2025-08-30 08:31:00',
            '2025-08-30 08:32:00',
            '2025-08-30 08:33:00',
            '2025-08-30 08:34:00',
            '2025-08-30 08:35:00',
            '2025-08-30 08:36:00',
            '2025-08-30 08:37:00',
            '2025-08-30 08:38:00',
            '2025-08-30 08:39:00',
            '2025-08-30 08:40:00',
            '2025-08-30 08:41:00',
            '2025-08-30 08:42:00',
            '2025-08-30 08:43:00',
            '2025-08-30 08:44:00',
            '2025-08-30 08:45:00',
            '2025-08-30 08:46:00',
            '2025-08-30 08:47:00',
            '2025-08-30 08:48:00',
            '2025-08-30 08:49:00',
            '2025-08-30 08:50:00',
            '2025-08-30 08:51:00',
            '2025-08-30 08:52:00',
            '2025-08-30 08:53:00',
            '2025-08-30 08:54:00',
            '2025-08-30 08:55:00',
            '2025-08-30 08:56:00',
            '2025-08-30 08:57:00',
            '2025-08-30 08:58:00',
            '2025-08-30 08:59:00',
            '2025-08-30 09:00:00',
            '2025-08-30 09:01:00',
            '2025-08-30 09:02:00',
            '2025-08-30 09:03:00',
            '2025-08-30 09:04:00',
            '2025-08-30 09:05:00',
            '2025-08-30 09:06:00',
            '2025-08-30 09:07:00',
            '2025-08-30 09:08:00',
            '2025-08-30 09:09:00',
            '2025-08-30 09:10:00',
            '2025-08-30 09:11:00',
            '2025-08-30 09:12:00',
            '2025-08-30 09:13:00',
            '2025-08-30 09:14:00',
            '2025-08-30 09:15:00',
            '2025-08-30 09:16:00',
            '2025-08-30 09:17:00',
            '2025-08-30 09:18:00',
            '2025-08-30 09:19:00',
            '2025-08-30 09:20:00',
            '2025-08-30 09:21:00',
            '2025-08-30 09:22:00',
            '2025-08-30 09:23:00',
            '2025-08-30 09:24:00',
            '2025-08-30 09:25:00',
            '2025-08-30 09:26:00',
            '2025-08-30 09:27:00',
            '2025-08-30 09:28:00',
            '2025-08-30 09:29:00',
            '2025-08-30 09:30:00',
            '2025-08-30 09:31:00',
            '2025-08-30 09:32:00',
            '2025-08-30 09:33:00',
            '2025-08-30 09:34:00',
            '2025-08-30 09:35:00',
            '2025-08-30 09:36:00',
            '2025-08-30 09:37:00',
            '2025-08-30 09:38:00',
            '2025-08-30 09:39:00',
            '2025-08-30 09:40:00',
            '2025-08-30 09:41:00',
            '2025-08-30 09:42:00',
            '2025-08-30 09:43:00',
            '2025-08-30 09:44:00',
            '2025-08-30 09:45:00',
            '2025-08-30 09:46:00',
            '2025-08-30 09:47:00',
            '2025-08-30 09:48:00',
            '2025-08-30 09:49:00',
            '2025-08-30 09:50:00',
            '2025-08-30 09:51:00',
            '2025-08-30 09:52:00',
            '2025-08-30 09:53:00',
            '2025-08-30 09:54:00',
            '2025-08-30 09:55:00',
            '2025-08-30 09:56:00',
            '2025-08-30 09:57:00',
            '2025-08-30 09:58:00',
            '2025-08-30 09:59:00',
            '2025-08-30 10:00:00',
            '2025-08-30 10:01:00',
            '2025-08-30 10:02:00',
            '2025-08-30 10:03:00',
            '2025-08-30 10:04:00',
            '2025-08-30 10:05:00',
            '2025-08-30 10:06:00',
            '2025-08-30 10:07:00',
            '2025-08-30 10:08:00',
            '2025-08-30 10:09:00',
            '2025-08-30 10:10:00',
            '2025-08-30 10:11:00',
            '2025-08-30 10:12:00',
            '2025-08-30 10:13:00',
            '2025-08-30 10:14:00',
            '2025-08-30 10:15:00',
            '2025-08-30 10:16:00',
            '2025-08-30 10:17:00',
            '2025-08-30 10:18:00',
            '2025-08-30 10:19:00',
            '2025-08-30 10:20:00',
            '2025-08-30 10:21:00',
            '2025-08-30 10:22:00',
            '2025-08-30 10:23:00',
            '2025-08-30 10:24:00',
            '2025-08-30 10:25:00',
            '2025-08-30 10:26:00',
            '2025-08-30 10:27:00',
            '2025-08-30 10:28:00',
            '2025-08-30 10:29:00',
            '2025-08-30 10:30:00',
            '2025-08-30 10:31:00',
            '2025-08-30 10:32:00',
            '2025-08-30 10:33:00',
            '2025-08-30 10:34:00',
            '2025-08-30 10:35:00',
            '2025-08-30 10:36:00',
            '2025-08-30 10:37:00',
            '2025-08-30 10:38:00',
            '2025-08-30 10:39:00',
            '2025-08-30 10:40:00',
            '2025-08-30 10:41:00',
            '2025-08-30 10:42:00',
            '2025-08-30 10:43:00',
            '2025-08-30 10:44:00',
            '2025-08-30 10:45:00',
            '2025-08-30 10:46:00',
            '2025-08-30 10:47:00',
            '2025-08-30 10:48:00',
            '2025-08-30 10:49:00',
            '2025-08-30 10:50:00',
            '2025-08-30 10:51:00',
            '2025-08-30 10:52:00',
            '2025-08-30 10:53:00',
            '2025-08-30 10:54:00',
            '2025-08-30 10:55:00',
            '2025-08-30 10:56:00',
            '2025-08-30 10:57:00',
            '2025-08-30 10:58:00',
            '2025-08-30 10:59:00',
            '2025-08-30 11:00:00',
            '2025-08-30 11:01:00',
            '2025-08-30 11:02:00',
            '2025-08-30 11:03:00',
            '2025-08-30 11:04:00',
            '2025-08-30 11:05:00',
            '2025-08-30 11:06:00',
            '2025-08-30 11:07:00',
            '2025-08-30 11:08:00',
            '2025-08-30 11:09:00',
            '2025-08-30 11:10:00',
            '2025-08-30 11:11:00',
            '2025-08-30 11:12:00',
            '2025-08-30 11:13:00',
            '2025-08-30 11:14:00',
            '2025-08-30 11:15:00',
            '2025-08-30 11:16:00',
            '2025-08-30 11:17:00',
            '2025-08-30 11:18:00',
            '2025-08-30 11:19:00',
            '2025-08-30 11:20:00',
            '2025-08-30 11:21:00',
            '2025-08-30 11:22:00',
            '2025-08-30 11:23:00',
            '2025-08-30 11:24:00',
            '2025-08-30 11:25:00',
            '2025-08-30 11:26:00',
            '2025-08-30 11:27:00',
            '2025-08-30 11:28:00',
            '2025-08-30 11:29:00',
            '2025-08-30 11:30:00',
            '2025-08-30 11:31:00',
            '2025-08-30 11:32:00',
            '2025-08-30 11:33:00',
            '2025-08-30 11:34:00',
            '2025-08-30 11:35:00',
            '2025-08-30 11:36:00',
            '2025-08-30 11:37:00',
            '2025-08-30 11:38:00',
            '2025-08-30 11:39:00',
            '2025-08-30 11:40:00',
            '2025-08-30 11:41:00',
            '2025-08-30 11:42:00',
            '2025-08-30 11:43:00',
            '2025-08-30 11:44:00',
            '2025-08-30 11:45:00',
            '2025-08-30 11:46:00',
            '2025-08-30 11:47:00',
            '2025-08-30 11:48:00',
            '2025-08-30 11:49:00',
            '2025-08-30 11:50:00',
            '2025-08-30 11:51:00',
            '2025-08-30 11:52:00',
            '2025-08-30 11:53:00',
            '2025-08-30 11:54:00',
            '2025-08-30 11:55:00',
            '2025-08-30 11:56:00',
            '2025-08-30 11:57:00',
            '2025-08-30 11:58:00',
            '2025-08-30 11:59:00',
            '2025-08-30 12:00:00',
            '2025-08-30 12:01:00',
            '2025-08-30 12:02:00',
            '2025-08-30 12:03:00',
            '2025-08-30 12:04:00',
            '2025-08-30 12:05:00',
            '2025-08-30 12:06:00',
            '2025-08-30 12:07:00',
            '2025-08-30 12:08:00',
            '2025-08-30 12:09:00',
            '2025-08-30 12:10:00',
            '2025-08-30 12:11:00',
            '2025-08-30 12:12:00',
            '2025-08-30 12:13:00',
            '2025-08-30 12:14:00',
            '2025-08-30 12:15:00',
            '2025-08-30 12:16:00',
            '2025-08-30 12:17:00',
            '2025-08-30 12:18:00',
            '2025-08-30 12:19:00',
            '2025-08-30 12:20:00',
            '2025-08-30 12:21:00',
            '2025-08-30 12:22:00',
            '2025-08-30 12:23:00',
            '2025-08-30 12:24:00',
            '2025-08-30 12:25:00',
            '2025-08-30 12:26:00',
            '2025-08-30 12:27:00',
            '2025-08-30 12:28:00',
            '2025-08-30 12:29:00',
            '2025-08-30 12:30:00',
            '2025-08-30 12:31:00',
            '2025-08-30 12:32:00',
            '2025-08-30 12:33:00',
            '2025-08-30 12:34:00',
            '2025-08-30 12:35:00',
            '2025-08-30 12:36:00',
            '2025-08-30 12:37:00',
            '2025-08-30 12:38:00',
            '2025-08-30 12:39:00',
            '2025-08-30 12:40:00',
            '2025-08-30 12:41:00',
            '2025-08-30 12:42:00',
            '2025-08-30 12:43:00',
            '2025-08-30 12:44:00',
            '2025-08-30 12:45:00',
            '2025-08-30 12:46:00',
            '2025-08-30 12:47:00',
            '2025-08-30 12:48:00',
            '2025-08-30 12:49:00',
            '2025-08-30 12:50:00',
            '2025-08-30 12:51:00',
            '2025-08-30 12:52:00',
            '2025-08-30 12:53:00',
            '2025-08-30 12:54:00',
            '2025-08-30 12:55:00',
            '2025-08-30 12:56:00',
            '2025-08-30 12:57:00',
            '2025-08-30 12:58:00',
            '2025-08-30 12:59:00',
            '2025-08-30 13:00:00',
            '2025-08-30 13:01:00',
            '2025-08-30 13:02:00',
            '2025-08-30 13:03:00',
            '2025-08-30 13:04:00',
            '2025-08-30 13:05:00',
            '2025-08-30 13:06:00',
            '2025-08-30 13:07:00',
            '2025-08-30 13:08:00',
            '2025-08-30 13:09:00',
            '2025-08-30 13:10:00',
            '2025-08-30 13:11:00',
            '2025-08-30 13:12:00',
            '2025-08-30 13:13:00',
            '2025-08-30 13:14:00',
            '2025-08-30 13:15:00',
            '2025-08-30 13:16:00',
            '2025-08-30 13:17:00',
            '2025-08-30 13:18:00',
            '2025-08-30 13:19:00',
            '2025-08-30 13:20:00',
            '2025-08-30 13:21:00',
            '2025-08-30 13:22:00',
            '2025-08-30 13:23:00',
            '2025-08-30 13:24:00',
            '2025-08-30 13:25:00',
            '2025-08-30 13:26:00',
            '2025-08-30 13:27:00',
            '2025-08-30 13:28:00',
            '2025-08-30 13:29:00',
            '2025-08-30 13:30:00',
            '2025-08-30 13:31:00',
            '2025-08-30 13:32:00',
            '2025-08-30 13:33:00',
            '2025-08-30 13:34:00',
            '2025-08-30 13:35:00',
            '2025-08-30 13:36:00',
            '2025-08-30 13:37:00',
            '2025-08-30 13:38:00',
            '2025-08-30 13:39:00',
            '2025-08-30 13:40:00',
            '2025-08-30 13:41:00',
            '2025-08-30 13:42:00',
            '2025-08-30 13:43:00',
            '2025-08-30 13:44:00',
            '2025-08-30 13:45:00',
            '2025-08-30 13:46:00',
            '2025-08-30 13:47:00',
            '2025-08-30 13:48:00',
            '2025-08-30 13:49:00',
            '2025-08-30 13:50:00',
            '2025-08-30 13:51:00',
            '2025-08-30 13:52:00',
            '2025-08-30 13:53:00',
            '2025-08-30 13:54:00',
            '2025-08-30 13:55:00',
            '2025-08-30 13:56:00',
            '2025-08-30 13:57:00',
            '2025-08-30 13:58:00',
            '2025-08-30 13:59:00',
            '2025-08-30 14:00:00',
            '2025-08-30 14:01:00',
            '2025-08-30 14:02:00',
            '2025-08-30 14:03:00',
            '2025-08-30 14:04:00',
            '2025-08-30 14:05:00',
            '2025-08-30 14:06:00',
            '2025-08-30 14:07:00',
            '2025-08-30 14:08:00',
            '2025-08-30 14:09:00',
            '2025-08-30 14:10:00',
            '2025-08-30 14:11:00',
            '2025-08-30 14:12:00',
            '2025-08-30 14:13:00',
            '2025-08-30 14:14:00',
            '2025-08-30 14:15:00',
            '2025-08-30 14:16:00',
            '2025-08-30 14:17:00',
            '2025-08-30 14:18:00',
            '2025-08-30 14:19:00',
            '2025-08-30 14:20:00',
            '2025-08-30 14:21:00',
            '2025-08-30 14:22:00',
            '2025-08-30 14:23:00',
            '2025-08-30 14:24:00',
            '2025-08-30 14:25:00',
            '2025-08-30 14:26:00',
            '2025-08-30 14:27:00',
            '2025-08-30 14:28:00',
            '2025-08-30 14:29:00',
            '2025-08-30 14:30:00',
            '2025-08-30 14:31:00',
            '2025-08-30 14:32:00',
            '2025-08-30 14:33:00',
            '2025-08-30 14:34:00',
            '2025-08-30 14:35:00',
            '2025-08-30 14:36:00',
            '2025-08-30 14:37:00',
            '2025-08-30 14:38:00',
            '2025-08-30 14:39:00',
            '2025-08-30 14:40:00',
            '2025-08-30 14:41:00',
            '2025-08-30 14:42:00',
            '2025-08-30 14:43:00',
            '2025-08-30 14:44:00',
            '2025-08-30 14:45:00',
            '2025-08-30 14:46:00',
            '2025-08-30 14:47:00',
            '2025-08-30 14:48:00',
            '2025-08-30 14:49:00',
            '2025-08-30 14:50:00',
            '2025-08-30 14:51:00',
            '2025-08-30 14:52:00',
            '2025-08-30 14:53:00',
            '2025-08-30 14:54:00',
            '2025-08-30 14:55:00',
            '2025-08-30 14:56:00',
            '2025-08-30 14:57:00',
            '2025-08-30 14:58:00',
            '2025-08-30 14:59:00',
            '2025-08-30 15:00:00',
            '2025-08-30 15:01:00',
            '2025-08-30 15:02:00',
            '2025-08-30 15:03:00',
            '2025-08-30 15:04:00',
            '2025-08-30 15:05:00',
            '2025-08-30 15:06:00',
            '2025-08-30 15:07:00',
            '2025-08-30 15:08:00',
            '2025-08-30 15:09:00',
            '2025-08-30 15:10:00',
            '2025-08-30 15:11:00',
            '2025-08-30 15:12:00',
            '2025-08-30 15:13:00',
            '2025-08-30 15:14:00',
            '2025-08-30 15:15:00',
            '2025-08-30 15:16:00',
            '2025-08-30 15:17:00',
            '2025-08-30 15:18:00',
            '2025-08-30 15:19:00',
            '2025-08-30 15:20:00',
            '2025-08-30 15:21:00',
            '2025-08-30 15:22:00',
            '2025-08-30 15:23:00',
            '2025-08-30 15:24:00',
            '2025-08-30 15:25:00',
            '2025-08-30 15:26:00',
            '2025-08-30 15:27:00',
            '2025-08-30 15:28:00',
            '2025-08-30 15:29:00',
            '2025-08-30 15:30:00',
            '2025-08-30 15:31:00',
            '2025-08-30 15:32:00',
            '2025-08-30 15:33:00',
            '2025-08-30 15:34:00',
            '2025-08-30 15:35:00',
            '2025-08-30 15:36:00',
            '2025-08-30 15:37:00',
            '2025-08-30 15:38:00',
            '2025-08-30 15:39:00',
            '2025-08-30 15:40:00',
            '2025-08-30 15:41:00',
            '2025-08-30 15:42:00',
            '2025-08-30 15:43:00',
            '2025-08-30 15:44:00',
            '2025-08-30 15:45:00',
            '2025-08-30 15:46:00',
            '2025-08-30 15:47:00',
            '2025-08-30 15:48:00',
            '2025-08-30 15:49:00',
            '2025-08-30 15:50:00',
            '2025-08-30 15:51:00',
            '2025-08-30 15:52:00',
            '2025-08-30 15:53:00',
            '2025-08-30 15:54:00',
            '2025-08-30 15:55:00',
            '2025-08-30 15:56:00',
            '2025-08-30 15:57:00',
            '2025-08-30 15:58:00',
            '2025-08-30 15:59:00',
            '2025-08-30 16:00:00',
            '2025-08-30 16:01:00',
            '2025-08-30 16:02:00',
            '2025-08-30 16:03:00',
            '2025-08-30 16:04:00',
            '2025-08-30 16:05:00',
            '2025-08-30 16:06:00',
            '2025-08-30 16:07:00',
            '2025-08-30 16:08:00',
            '2025-08-30 16:09:00',
            '2025-08-30 16:10:00',
            '2025-08-30 16:11:00',
            '2025-08-30 16:12:00',
            '2025-08-30 16:13:00',
            '2025-08-30 16:14:00',
            '2025-08-30 16:15:00',
            '2025-08-30 16:16:00',
            '2025-08-30 16:17:00',
            '2025-08-30 16:18:00',
            '2025-08-30 16:19:00',
            '2025-08-30 16:20:00',
            '2025-08-30 16:21:00',
            '2025-08-30 16:22:00',
            '2025-08-30 16:23:00',
            '2025-08-30 16:24:00',
            '2025-08-30 16:25:00',
            '2025-08-30 16:26:00',
            '2025-08-30 16:27:00',
            '2025-08-30 16:28:00',
            '2025-08-30 16:29:00',
            '2025-08-30 16:30:00',
            '2025-08-30 16:31:00',
            '2025-08-30 16:32:00',
            '2025-08-30 16:33:00',
            '2025-08-30 16:34:00',
            '2025-08-30 16:35:00',
            '2025-08-30 16:36:00',
            '2025-08-30 16:37:00',
            '2025-08-30 16:38:00',
            '2025-08-30 16:39:00',
            '2025-08-30 16:40:00',
            '2025-08-30 16:41:00',
            '2025-08-30 16:42:00',
            '2025-08-30 16:43:00',
            '2025-08-30 16:44:00',
            '2025-08-30 16:45:00',
            '2025-08-30 16:46:00',
            '2025-08-30 16:47:00',
            '2025-08-30 16:48:00',
            '2025-08-30 16:49:00',
            '2025-08-30 16:50:00',
            '2025-08-30 16:51:00',
            '2025-08-30 16:52:00',
            '2025-08-30 16:53:00',
            '2025-08-30 16:54:00',
            '2025-08-30 16:55:00',
            '2025-08-30 16:56:00',
            '2025-08-30 16:57:00',
            '2025-08-30 16:58:00',
            '2025-08-30 16:59:00',
            '2025-08-30 17:00:00',
            '2025-08-30 17:01:00',
            '2025-08-30 17:02:00',
            '2025-08-30 17:03:00',
            '2025-08-30 17:04:00',
            '2025-08-30 17:05:00',
            '2025-08-30 17:06:00',
            '2025-08-30 17:07:00',
            '2025-08-30 17:08:00',
            '2025-08-30 17:09:00',
            '2025-08-30 17:10:00',
            '2025-08-30 17:11:00',
            '2025-08-30 17:12:00',
            '2025-08-30 17:13:00',
            '2025-08-30 17:14:00',
            '2025-08-30 17:15:00',
            '2025-08-30 17:16:00',
            '2025-08-30 17:17:00',
            '2025-08-30 17:18:00',
            '2025-08-30 17:19:00',
            '2025-08-30 17:20:00',
            '2025-08-30 17:21:00',
            '2025-08-30 17:22:00',
            '2025-08-30 17:23:00',
            '2025-08-30 17:24:00',
            '2025-08-30 17:25:00',
            '2025-08-30 17:26:00',
            '2025-08-30 17:27:00',
            '2025-08-30 17:28:00',
            '2025-08-30 17:29:00',
            '2025-08-30 17:30:00',
            '2025-08-30 17:31:00',
            '2025-08-30 17:32:00',
            '2025-08-30 17:33:00',
            '2025-08-30 17:34:00',
            '2025-08-30 17:35:00',
            '2025-08-30 17:36:00',
            '2025-08-30 17:37:00',
            '2025-08-30 17:38:00',
            '2025-08-30 17:39:00',
            '2025-08-30 17:40:00',
            '2025-08-30 17:41:00',
            '2025-08-30 17:42:00',
            '2025-08-30 17:43:00',
            '2025-08-30 17:44:00',
            '2025-08-30 17:45:00',
            '2025-08-30 17:46:00',
            '2025-08-30 17:47:00',
            '2025-08-30 17:48:00',
            '2025-08-30 17:49:00',
            '2025-08-30 17:50:00',
            '2025-08-30 17:51:00',
            '2025-08-30 17:52:00',
            '2025-08-30 17:53:00',
            '2025-08-30 17:54:00',
            '2025-08-30 17:55:00',
            '2025-08-30 17:56:00',
            '2025-08-30 17:57:00',
            '2025-08-30 17:58:00',
            '2025-08-30 17:59:00',
            '2025-08-30 18:00:00',
            '2025-08-30 18:01:00',
            '2025-08-30 18:02:00',
            '2025-08-30 18:03:00',
            '2025-08-30 18:04:00',
            '2025-08-30 18:05:00',
            '2025-08-30 18:06:00',
            '2025-08-30 18:07:00',
            '2025-08-30 18:08:00',
            '2025-08-30 18:09:00',
            '2025-08-30 18:10:00',
            '2025-08-30 18:11:00',
            '2025-08-30 18:12:00',
            '2025-08-30 18:13:00',
            '2025-08-30 18:14:00',
            '2025-08-30 18:15:00',
            '2025-08-30 18:16:00',
            '2025-08-30 18:17:00',
            '2025-08-30 18:18:00',
            '2025-08-30 18:19:00',
            '2025-08-30 18:20:00',
            '2025-08-30 18:21:00',
            '2025-08-30 18:22:00',
            '2025-08-30 18:23:00',
            '2025-08-30 18:24:00',
            '2025-08-30 18:25:00',
            '2025-08-30 18:26:00',
            '2025-08-30 18:27:00',
            '2025-08-30 18:28:00',
            '2025-08-30 18:29:00',
            '2025-08-30 18:30:00',
            '2025-08-30 18:31:00',
            '2025-08-30 18:32:00',
            '2025-08-30 18:33:00',
            '2025-08-30 18:34:00',
            '2025-08-30 18:35:00',
            '2025-08-30 18:36:00',
            '2025-08-30 18:37:00',
            '2025-08-30 18:38:00',
            '2025-08-30 18:39:00',
            '2025-08-30 18:40:00',
            '2025-08-30 18:41:00',
            '2025-08-30 18:42:00',
            '2025-08-30 18:43:00',
            '2025-08-30 18:44:00',
            '2025-08-30 18:45:00',
            '2025-08-30 18:46:00',
            '2025-08-30 18:47:00',
            '2025-08-30 18:48:00',
            '2025-08-30 18:49:00',
            '2025-08-30 18:50:00',
            '2025-08-30 18:51:00',
            '2025-08-30 18:52:00',
            '2025-08-30 18:53:00',
            '2025-08-30 18:54:00',
            '2025-08-30 18:55:00',
            '2025-08-30 18:56:00',
            '2025-08-30 18:57:00',
            '2025-08-30 18:58:00',
            '2025-08-30 18:59:00',
            '2025-08-30 19:00:00',
            '2025-08-30 19:01:00',
            '2025-08-30 19:02:00',
            '2025-08-30 19:03:00',
            '2025-08-30 19:04:00',
            '2025-08-30 19:05:00',
            '2025-08-30 19:06:00',
            '2025-08-30 19:07:00',
            '2025-08-30 19:08:00',
            '2025-08-30 19:09:00',
            '2025-08-30 19:10:00',
            '2025-08-30 19:11:00',
            '2025-08-30 19:12:00',
            '2025-08-30 19:13:00',
            '2025-08-30 19:14:00',
            '2025-08-30 19:15:00',
            '2025-08-30 19:16:00',
            '2025-08-30 19:17:00',
            '2025-08-30 19:18:00',
            '2025-08-30 19:19:00',
            '2025-08-30 19:20:00',
            '2025-08-30 19:21:00',
            '2025-08-30 19:22:00',
            '2025-08-30 19:23:00',
            '2025-08-30 19:24:00',
            '2025-08-30 19:25:00',
            '2025-08-30 19:26:00',
            '2025-08-30 19:27:00',
            '2025-08-30 19:28:00',
            '2025-08-30 19:29:00',
            '2025-08-30 19:30:00',
            '2025-08-30 19:31:00',
            '2025-08-30 19:32:00',
            '2025-08-30 19:33:00',
            '2025-08-30 19:34:00',
            '2025-08-30 19:35:00',
            '2025-08-30 19:36:00',
            '2025-08-30 19:37:00',
            '2025-08-30 19:38:00',
            '2025-08-30 19:39:00',
            '2025-08-30 19:40:00',
            '2025-08-30 19:41:00',
            '2025-08-30 19:42:00',
            '2025-08-30 19:43:00',
            '2025-08-30 19:44:00',
            '2025-08-30 19:45:00',
            '2025-08-30 19:46:00',
            '2025-08-30 19:47:00',
            '2025-08-30 19:48:00',
            '2025-08-30 19:49:00',
            '2025-08-30 19:50:00',
            '2025-08-30 19:51:00',
            '2025-08-30 19:52:00',
            '2025-08-30 19:53:00',
            '2025-08-30 19:54:00',
            '2025-08-30 19:55:00',
            '2025-08-30 19:56:00',
            '2025-08-30 19:57:00',
            '2025-08-30 19:58:00',
            '2025-08-30 19:59:00',
            '2025-08-30 20:00:00',
            '2025-08-30 20:01:00',
            '2025-08-30 20:02:00',
            '2025-08-30 20:03:00',
            '2025-08-30 20:04:00',
            '2025-08-30 20:05:00',
            '2025-08-30 20:06:00',
            '2025-08-30 20:07:00',
            '2025-08-30 20:08:00',
            '2025-08-30 20:09:00',
            '2025-08-30 20:10:00',
            '2025-08-30 20:11:00',
            '2025-08-30 20:12:00',
            '2025-08-30 20:13:00',
            '2025-08-30 20:14:00',
            '2025-08-30 20:15:00',
            '2025-08-30 20:16:00',
            '2025-08-30 20:17:00',
            '2025-08-30 20:18:00',
            '2025-08-30 20:19:00',
            '2025-08-30 20:20:00',
            '2025-08-30 20:21:00',
            '2025-08-30 20:22:00',
            '2025-08-30 20:23:00',
            '2025-08-30 20:24:00',
            '2025-08-30 20:25:00',
            '2025-08-30 20:26:00',
            '2025-08-30 20:27:00',
            '2025-08-30 20:28:00',
            '2025-08-30 20:29:00',
            '2025-08-30 20:30:00',
            '2025-08-30 20:31:00',
            '2025-08-30 20:32:00',
            '2025-08-30 20:33:00',
            '2025-08-30 20:34:00',
            '2025-08-30 20:35:00',
            '2025-08-30 20:36:00',
            '2025-08-30 20:37:00',
            '2025-08-30 20:38:00',
            '2025-08-30 20:39:00',
            '2025-08-30 20:40:00',
            '2025-08-30 20:41:00',
            '2025-08-30 20:42:00',
            '2025-08-30 20:43:00',
            '2025-08-30 20:44:00',
            '2025-08-30 20:45:00',
            '2025-08-30 20:46:00',
            '2025-08-30 20:47:00',
            '2025-08-30 20:48:00',
            '2025-08-30 20:49:00',
            '2025-08-30 20:50:00',
            '2025-08-30 20:51:00',
            '2025-08-30 20:52:00',
            '2025-08-30 20:53:00',
            '2025-08-30 20:54:00',
            '2025-08-30 20:55:00',
            '2025-08-30 20:56:00',
            '2025-08-30 20:57:00',
            '2025-08-30 20:58:00',
            '2025-08-30 20:59:00',
            '2025-08-30 21:00:00',
            '2025-08-30 21:01:00',
            '2025-08-30 21:02:00',
            '2025-08-30 21:03:00',
            '2025-08-30 21:04:00',
            '2025-08-30 21:05:00',
            '2025-08-30 21:06:00',
            '2025-08-30 21:07:00',
            '2025-08-30 21:08:00',
            '2025-08-30 21:09:00',
            '2025-08-30 21:10:00',
            '2025-08-30 21:11:00',
            '2025-08-30 21:12:00',
            '2025-08-30 21:13:00',
            '2025-08-30 21:14:00',
            '2025-08-30 21:15:00',
            '2025-08-30 21:16:00',
            '2025-08-30 21:17:00',
            '2025-08-30 21:18:00',
            '2025-08-30 21:19:00',
            '2025-08-30 21:20:00',
            '2025-08-30 21:21:00',
            '2025-08-30 21:22:00',
            '2025-08-30 21:23:00',
            '2025-08-30 21:24:00',
            '2025-08-30 21:25:00',
            '2025-08-30 21:26:00',
            '2025-08-30 21:27:00',
            '2025-08-30 21:28:00',
            '2025-08-30 21:29:00',
            '2025-08-30 21:30:00',
            '2025-08-30 21:31:00',
            '2025-08-30 21:32:00',
            '2025-08-30 21:33:00',
            '2025-08-30 21:34:00',
            '2025-08-30 21:35:00',
            '2025-08-30 21:36:00',
            '2025-08-30 21:37:00',
            '2025-08-30 21:38:00',
            '2025-08-30 21:39:00',
            '2025-08-30 21:40:00',
            '2025-08-30 21:41:00',
            '2025-08-30 21:42:00',
            '2025-08-30 21:43:00',
            '2025-08-30 21:44:00',
            '2025-08-30 21:45:00',
            '2025-08-30 21:46:00',
            '2025-08-30 21:47:00',
            '2025-08-30 21:48:00',
            '2025-08-30 21:49:00',
            '2025-08-30 21:50:00',
            '2025-08-30 21:51:00',
            '2025-08-30 21:52:00',
            '2025-08-30 21:53:00',
            '2025-08-30 21:54:00',
            '2025-08-30 21:55:00',
            '2025-08-30 21:56:00',
            '2025-08-30 21:57:00',
            '2025-08-30 21:58:00',
            '2025-08-30 21:59:00',
            '2025-08-30 22:00:00',
            '2025-08-30 22:01:00',
            '2025-08-30 22:02:00',
            '2025-08-30 22:03:00',
            '2025-08-30 22:04:00',
            '2025-08-30 22:05:00',
            '2025-08-30 22:06:00',
            '2025-08-30 22:07:00',
            '2025-08-30 22:08:00',
            '2025-08-30 22:09:00',
            '2025-08-30 22:10:00',
            '2025-08-30 22:11:00',
            '2025-08-30 22:12:00',
            '2025-08-30 22:13:00',
            '2025-08-30 22:14:00',
            '2025-08-30 22:15:00',
            '2025-08-30 22:16:00',
            '2025-08-30 22:17:00',
            '2025-08-30 22:18:00',
            '2025-08-30 22:19:00',
            '2025-08-30 22:20:00',
            '2025-08-30 22:21:00',
            '2025-08-30 22:22:00',
            '2025-08-30 22:23:00',
            '2025-08-30 22:24:00',
            '2025-08-30 22:25:00',
            '2025-08-30 22:26:00',
            '2025-08-30 22:27:00',
            '2025-08-30 22:28:00',
            '2025-08-30 22:29:00',
            '2025-08-30 22:30:00',
            '2025-08-30 22:31:00',
            '2025-08-30 22:32:00',
            '2025-08-30 22:33:00',
            '2025-08-30 22:34:00',
            '2025-08-30 22:35:00',
            '2025-08-30 22:36:00',
            '2025-08-30 22:37:00',
            '2025-08-30 22:38:00',
            '2025-08-30 22:39:00',
            '2025-08-30 22:40:00',
            '2025-08-30 22:41:00',
            '2025-08-30 22:42:00',
            '2025-08-30 22:43:00',
            '2025-08-30 22:44:00',
            '2025-08-30 22:45:00',
            '2025-08-30 22:46:00',
            '2025-08-30 22:47:00',
            '2025-08-30 22:48:00',
            '2025-08-30 22:49:00',
            '2025-08-30 22:50:00',
            '2025-08-30 22:51:00',
            '2025-08-30 22:52:00',
            '2025-08-30 22:53:00',
            '2025-08-30 22:54:00',
            '2025-08-30 22:55:00',
            '2025-08-30 22:56:00',
            '2025-08-30 22:57:00',
            '2025-08-30 22:58:00',
            '2025-08-30 22:59:00',
            '2025-08-30 23:00:00',
            '2025-08-30 23:01:00',
            '2025-08-30 23:02:00',
            '2025-08-30 23:03:00',
            '2025-08-30 23:04:00',
            '2025-08-30 23:05:00',
            '2025-08-30 23:06:00',
            '2025-08-30 23:07:00',
            '2025-08-30 23:08:00',
            '2025-08-30 23:09:00',
            '2025-08-30 23:10:00',
            '2025-08-30 23:11:00',
            '2025-08-30 23:12:00',
            '2025-08-30 23:13:00',
            '2025-08-30 23:14:00',
            '2025-08-30 23:15:00',
            '2025-08-30 23:16:00',
            '2025-08-30 23:17:00',
            '2025-08-30 23:18:00',
            '2025-08-30 23:19:00',
            '2025-08-30 23:20:00',
            '2025-08-30 23:21:00',
            '2025-08-30 23:22:00',
            '2025-08-30 23:23:00',
            '2025-08-30 23:24:00',
            '2025-08-30 23:25:00',
            '2025-08-30 23:26:00',
            '2025-08-30 23:27:00',
            '2025-08-30 23:28:00',
            '2025-08-30 23:29:00',
            '2025-08-30 23:30:00',
            '2025-08-30 23:31:00',
            '2025-08-30 23:32:00',
            '2025-08-30 23:33:00',
            '2025-08-30 23:34:00',
            '2025-08-30 23:35:00',
            '2025-08-30 23:36:00',
            '2025-08-30 23:37:00',
            '2025-08-30 23:38:00',
            '2025-08-30 23:39:00',
            '2025-08-30 23:40:00',
            '2025-08-30 23:41:00',
            '2025-08-30 23:42:00',
            '2025-08-30 23:43:00',
            '2025-08-30 23:44:00',
            '2025-08-30 23:45:00',
            '2025-08-30 23:46:00',
            '2025-08-30 23:47:00',
            '2025-08-30 23:48:00',
            '2025-08-30 23:49:00',
            '2025-08-30 23:50:00',
            '2025-08-30 23:51:00',
            '2025-08-30 23:52:00',
            '2025-08-30 23:53:00',
            '2025-08-30 23:54:00',
            '2025-08-30 23:55:00',
            '2025-08-30 23:56:00',
            '2025-08-30 23:57:00',
            '2025-08-30 23:58:00',
            '2025-08-30 23:59:00'
          ],
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '压力 (MPa)',
            position: 'left',
            axisLabel: {
              formatter: '{value} MPa'
            },
            nameTextStyle: {
              color: '#5470c6'
            }
          },

          {
            type: 'value',
            name: '频率 (HZ)',
            position: 'right',
            axisLabel: {
              formatter: '{value} '
            },
            nameTextStyle: {
              color: '#5470c6'
            }
          },
          {
            type: 'value',
            name: '用水量（吨）',
            position: 'right',
            offset: 70,
            axisLabel: {
              formatter: '{value} '
            },
            nameTextStyle: {
              color: '#5470c6'
            }
          }
        ],

        series: [
          {
            name: '出口压力',
            type: 'line',
            data: [
              1.05, 1.04, 1.05, 1.05, 1.06, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.01, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.06, 1.05, 1.05, 1.05, 1.04, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.04, 1.06, 1.05, 1.05, 1.05, 1.04, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.06, 1.06, 1.06, 1.06, 1.05, 1.05, 1.04, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.04, 1.06, 1.05, 1.06, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.06, 1.06, 1.06, 1.07, 1.03, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05,
              1.04, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.04, 1.05, 1.04, 1.05, 1.06, 1.05, 1.04, 1.05, 1.04, 1.06, 1.05, 1.04, 1.05, 1.06, 1.04, 1.05, 1.05, 1.05, 1.04, 1.05,
              1.03, 1.06, 1.05, 1.06, 1.05, 1.06, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.06, 1.06, 1.05, 1.06, 1.06, 1.05, 1.05, 1.04, 1.05, 1.04, 1.04, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.06, 1.03, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.06, 1.04, 1.05, 1.05, 1.05, 1.06, 1.05, 1.07, 1.05, 1.07, 1.05, 1.03, 1.05, 1.05, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.06, 1.06, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.06, 1.07, 1.05, 1.03, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.04, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.04,
              1.05, 1.05, 1.04, 1.05, 1.05, 1.06, 1.05, 1.04, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.04, 1.04, 1.05, 1.06, 1.05,
              1.06, 1.06, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.03, 1.05, 1.05, 1.04, 1.06, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.02, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.04, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.04, 1.05, 1.06, 1.04, 1.05, 1.05, 1.06, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.06, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.04,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.06, 1.04, 1.04, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.03, 1.05, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.06, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.06, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.04, 1.06, 1.05, 1.05, 1.07, 1.06, 1.05, 1.06, 1.05, 1.05, 1.04, 1.07, 1.05, 1.04, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.04, 1.05,
              1.04, 1.05, 1.06, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.04, 1.05, 1.06, 1.06, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.04,
              1.05, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.04, 1.04, 1.05, 1.05, 1.06, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.06, 1.06, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.06, 1.06, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.06, 1.05, 1.05, 1.05, 1.03, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.04, 1.05, 1.05,
              1.04, 1.03, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.04, 1.05, 1.06, 1.05, 1.04, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.04, 1.05, 1.05, 1.05, 1.03, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.06, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.06, 1.06, 1.04, 1.05, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.04, 1.05, 1.06, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05,
              1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.06, 1.05, 1.05, 1.04, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05, 1.05
            ],
            smooth: true,
            lineStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '末端压力',
            type: 'line',
            data: [
              0.316, 0.323, 0.315, 0.32, 0.313, 0.323, 0.315, 0.3, 0.318, 0.315, 0.317, 0.315, 0.315, 0.308, 0.317, 0.314, 0.324, 0.316, 0.314, 0.311, 0.315, 0.322, 0.316, 0.326, 0.318, 0.316, 0.314, 0.319, 0.316, 0.317, 0.314, 0.315,
              0.315, 0.316, 0.314, 0.314, 0.315, 0.319, 0.318, 0.315, 0.316, 0.315, 0.316, 0.315, 0.316, 0.318, 0.315, 0.315, 0.314, 0.321, 0.314, 0.315, 0.316, 0.313, 0.315, 0.316, 0.304, 0.315, 0.315, 0.325, 0.32, 0.315, 0.315, 0.314,
              0.315, 0.316, 0.316, 0.316, 0.317, 0.321, 0.316, 0.316, 0.295, 0.31, 0.315, 0.316, 0.316, 0.316, 0.316, 0.315, 0.316, 0.315, 0.316, 0.316, 0.315, 0.316, 0.318, 0.317, 0.315, 0.316, 0.316, 0.316, 0.316, 0.315, 0.316, 0.316,
              0.316, 0.315, 0.315, 0.315, 0.318, 0.315, 0.326, 0.316, 0.316, 0.328, 0.316, 0.315, 0.316, 0.316, 0.319, 0.317, 0.315, 0.317, 0.314, 0.315, 0.316, 0.317, 0.315, 0.315, 0.316, 0.316, 0.316, 0.299, 0.316, 0.315, 0.317, 0.316,
              0.316, 0.316, 0.315, 0.316, 0.316, 0.316, 0.327, 0.32, 0.321, 0.316, 0.315, 0.305, 0.318, 0.316, 0.316, 0.316, 0.315, 0.315, 0.316, 0.316, 0.316, 0.316, 0.314, 0.315, 0.316, 0.316, 0.316, 0.315, 0.315, 0.316, 0.315, 0.316,
              0.315, 0.321, 0.316, 0.315, 0.316, 0.315, 0.315, 0.316, 0.317, 0.315, 0.316, 0.316, 0.317, 0.316, 0.316, 0.315, 0.315, 0.317, 0.317, 0.318, 0.315, 0.315, 0.315, 0.315, 0.316, 0.317, 0.316, 0.316, 0.316, 0.316, 0.316, 0.316,
              0.316, 0.316, 0.316, 0.316, 0.317, 0.314, 0.316, 0.316, 0.316, 0.315, 0.316, 0.316, 0.315, 0.316, 0.315, 0.316, 0.316, 0.318, 0.316, 0.316, 0.249, 0.285, 0.316, 0.316, 0.316, 0.316, 0.317, 0.317, 0.317, 0.316, 0.316, 0.315,
              0.316, 0.315, 0.317, 0.316, 0.316, 0.315, 0.317, 0.316, 0.315, 0.315, 0.316, 0.317, 0.317, 0.316, 0.319, 0.315, 0.316, 0.316, 0.316, 0.317, 0.316, 0.315, 0.315, 0.319, 0.317, 0.315, 0.313, 0.316, 0.316, 0.316, 0.317, 0.316,
              0.317, 0.316, 0.312, 0.316, 0.316, 0.316, 0.316, 0.317, 0.316, 0.317, 0.315, 0.316, 0.315, 0.317, 0.316, 0.317, 0.316, 0.331, 0.317, 0.316, 0.316, 0.316, 0.317, 0.317, 0.316, 0.316, 0.317, 0.317, 0.316, 0.315, 0.318, 0.317,
              0.315, 0.315, 0.316, 0.316, 0.317, 0.316, 0.317, 0.317, 0.315, 0.316, 0.316, 0.316, 0.316, 0.316, 0.316, 0.317, 0.315, 0.317, 0.316, 0.316, 0.328, 0.316, 0.318, 0.315, 0.313, 0.324, 0.317, 0.316, 0.316, 0.315, 0.315, 0.317,
              0.316, 0.317, 0.317, 0.318, 0.316, 0.306, 0.315, 0.316, 0.316, 0.317, 0.317, 0.318, 0.313, 0.318, 0.316, 0.317, 0.316, 0.318, 0.317, 0.317, 0.317, 0.316, 0.317, 0.316, 0.316, 0.318, 0.315, 0.317, 0.319, 0.315, 0.316, 0.316,
              0.317, 0.317, 0.318, 0.318, 0.317, 0.312, 0.317, 0.32, 0.315, 0.316, 0.325, 0.316, 0.326, 0.318, 0.325, 0.293, 0.317, 0.316, 0.319, 0.326, 0.316, 0.314, 0.32, 0.316, 0.312, 0.316, 0.315, 0.327, 0.299, 0.306, 0.315, 0.32,
              0.317, 0.318, 0.316, 0.317, 0.318, 0.3, 0.291, 0.319, 0.317, 0.305, 0.315, 0.294, 0.329, 0.312, 0.319, 0.313, 0.315, 0.313, 0.318, 0.319, 0.312, 0.319, 0.316, 0.313, 0.313, 0.311, 0.316, 0.314, 0.311, 0.272, 0.313, 0.316,
              0.317, 0.318, 0.314, 0.316, 0.311, 0.316, 0.315, 0.316, 0.3, 0.319, 0.288, 0.313, 0.319, 0.309, 0.304, 0.317, 0.323, 0.311, 0.321, 0.313, 0.344, 0.315, 0.318, 0.309, 0.316, 0.314, 0.301, 0.317, 0.318, 0.314, 0.313, 0.308,
              0.314, 0.316, 0.327, 0.331, 0.315, 0.324, 0.321, 0.307, 0.315, 0.316, 0.312, 0.314, 0.321, 0.348, 0.303, 0.315, 0.328, 0.365, 0.306, 0.314, 0.362, 0.334, 0.308, 0.303, 0.314, 0.329, 0.309, 0.313, 0.313, 0.326, 0.314, 0.318,
              0.312, 0.325, 0.297, 0.321, 0.318, 0.314, 0.316, 0.317, 0.313, 0.336, 0.316, 0.315, 0.308, 0.327, 0.299, 0.316, 0.311, 0.31, 0.331, 0.313, 0.309, 0.316, 0.315, 0.325, 0.315, 0.308, 0.308, 0.304, 0.291, 0.309, 0.315, 0.312,
              0.313, 0.308, 0.329, 0.32, 0.318, 0.328, 0.31, 0.317, 0.31, 0.327, 0.321, 0.33, 0.325, 0.316, 0.323, 0.288, 0.316, 0.325, 0.315, 0.323, 0.313, 0.32, 0.315, 0.32, 0.314, 0.316, 0.294, 0.331, 0.327, 0.304, 0.283, 0.322, 0.319,
              0.319, 0.329, 0.299, 0.316, 0.314, 0.311, 0.317, 0.307, 0.307, 0.319, 0.323, 0.311, 0.319, 0.308, 0.324, 0.316, 0.316, 0.318, 0.308, 0.304, 0.3, 0.315, 0.321, 0.29, 0.306, 0.309, 0.322, 0.312, 0.31, 0.309, 0.314, 0.32, 0.308,
              0.31, 0.336, 0.323, 0.313, 0.314, 0.325, 0.314, 0.307, 0.306, 0.324, 0.314, 0.315, 0.321, 0.317, 0.31, 0.315, 0.311, 0.306, 0.314, 0.317, 0.321, 0.332, 0.311, 0.338, 0.313, 0.308, 0.329, 0.319, 0.316, 0.313, 0.314, 0.318,
              0.314, 0.31, 0.309, 0.31, 0.312, 0.292, 0.314, 0.317, 0.32, 0.313, 0.329, 0.302, 0.315, 0.285, 0.31, 0.304, 0.317, 0.318, 0.311, 0.32, 0.319, 0.317, 0.315, 0.335, 0.315, 0.315, 0.315, 0.316, 0.313, 0.315, 0.315, 0.314, 0.315,
              0.315, 0.306, 0.314, 0.316, 0.329, 0.315, 0.32, 0.312, 0.318, 0.324, 0.308, 0.335, 0.313, 0.314, 0.319, 0.315, 0.295, 0.316, 0.305, 0.318, 0.315, 0.317, 0.316, 0.304, 0.316, 0.317, 0.317, 0.315, 0.309, 0.312, 0.317, 0.316,
              0.333, 0.318, 0.312, 0.315, 0.314, 0.327, 0.313, 0.309, 0.309, 0.309, 0.305, 0.324, 0.311, 0.307, 0.312, 0.323, 0.317, 0.318, 0.325, 0.316, 0.317, 0.321, 0.316, 0.315, 0.318, 0.313, 0.313, 0.318, 0.33, 0.315, 0.31, 0.301,
              0.311, 0.314, 0.316, 0.315, 0.319, 0.308, 0.309, 0.305, 0.318, 0.311, 0.307, 0.322, 0.314, 0.319, 0.307, 0.316, 0.297, 0.315, 0.319, 0.317, 0.319, 0.322, 0.314, 0.313, 0.317, 0.317, 0.315, 0.318, 0.319, 0.313, 0.312, 0.318,
              0.315, 0.316, 0.303, 0.313, 0.302, 0.309, 0.303, 0.319, 0.316, 0.295, 0.315, 0.315, 0.313, 0.3, 0.315, 0.312, 0.304, 0.318, 0.329, 0.306, 0.319, 0.295, 0.318, 0.308, 0.318, 0.311, 0.307, 0.329, 0.319, 0.313, 0.319, 0.317,
              0.318, 0.315, 0.327, 0.312, 0.322, 0.304, 0.32, 0.319, 0.309, 0.315, 0.312, 0.317, 0.309, 0.325, 0.317, 0.314, 0.317, 0.313, 0.32, 0.312, 0.315, 0.317, 0.304, 0.311, 0.323, 0.315, 0.317, 0.301, 0.317, 0.317, 0.317, 0.317,
              0.315, 0.32, 0.313, 0.307, 0.323, 0.316, 0.314, 0.314, 0.316, 0.315, 0.318, 0.315, 0.303, 0.32, 0.315, 0.316, 0.316, 0.316, 0.303, 0.314, 0.31, 0.314, 0.316, 0.315, 0.313, 0.327, 0.313, 0.315, 0.316, 0.314, 0.315, 0.318, 0.32,
              0.317, 0.349, 0.31, 0.318, 0.33, 0.32, 0.321, 0.315, 0.316, 0.3, 0.33, 0.311, 0.303, 0.311, 0.315, 0.316, 0.337, 0.318, 0.313, 0.316, 0.314, 0.315, 0.315, 0.325, 0.315, 0.316, 0.315, 0.315, 0.317, 0.316, 0.314, 0.316, 0.313,
              0.311, 0.324, 0.317, 0.324, 0.321, 0.328, 0.317, 0.316, 0.316, 0.317, 0.316, 0.318, 0.315, 0.321, 0.307, 0.312, 0.319, 0.313, 0.317, 0.31, 0.315, 0.317, 0.316, 0.315, 0.315, 0.314, 0.323, 0.327, 0.316, 0.318, 0.315, 0.318,
              0.318, 0.315, 0.315, 0.316, 0.322, 0.313, 0.315, 0.324, 0.324, 0.315, 0.316, 0.312, 0.3, 0.315, 0.315, 0.329, 0.322, 0.314, 0.316, 0.316, 0.316, 0.316, 0.315, 0.316, 0.315, 0.316, 0.315, 0.316, 0.324, 0.314, 0.315, 0.316,
              0.318, 0.32, 0.316, 0.315, 0.319, 0.315, 0.315, 0.314, 0.318, 0.333, 0.315, 0.314, 0.309, 0.317, 0.315, 0.308, 0.317, 0.316, 0.316, 0.313, 0.318, 0.334, 0.313, 0.307, 0.315, 0.319, 0.309, 0.317, 0.316, 0.316, 0.302, 0.316,
              0.318, 0.316, 0.314, 0.315, 0.316, 0.32, 0.319, 0.32, 0.314, 0.334, 0.304, 0.314, 0.314, 0.309, 0.323, 0.315, 0.317, 0.313, 0.315, 0.311, 0.335, 0.324, 0.318, 0.316, 0.318, 0.315, 0.307, 0.317, 0.314, 0.333, 0.323, 0.295,
              0.315, 0.316, 0.31, 0.317, 0.315, 0.312, 0.318, 0.301, 0.325, 0.309, 0.327, 0.312, 0.316, 0.316, 0.315, 0.314, 0.313, 0.316, 0.326, 0.315, 0.309, 0.322, 0.3, 0.316, 0.316, 0.324, 0.314, 0.312, 0.308, 0.329, 0.311, 0.315,
              0.316, 0.316, 0.316, 0.316, 0.317, 0.322, 0.317, 0.306, 0.315, 0.317, 0.314, 0.316, 0.321, 0.32, 0.293, 0.313, 0.316, 0.317, 0.319, 0.288, 0.312, 0.309, 0.316, 0.312, 0.303, 0.33, 0.317, 0.327, 0.319, 0.317, 0.314, 0.313,
              0.316, 0.29, 0.314, 0.311, 0.306, 0.326, 0.315, 0.314, 0.314, 0.316, 0.323, 0.331, 0.316, 0.311, 0.32, 0.309, 0.351, 0.313, 0.304, 0.327, 0.312, 0.304, 0.323, 0.308, 0.321, 0.316, 0.314, 0.316, 0.321, 0.319, 0.314, 0.304,
              0.303, 0.325, 0.31, 0.318, 0.307, 0.316, 0.323, 0.308, 0.318, 0.306, 0.316, 0.317, 0.319, 0.31, 0.313, 0.311, 0.314, 0.317, 0.322, 0.316, 0.309, 0.311, 0.313, 0.308, 0.317, 0.315, 0.313, 0.328, 0.313, 0.316, 0.302, 0.312,
              0.314, 0.316, 0.329, 0.316, 0.312, 0.313, 0.31, 0.346, 0.308, 0.315, 0.32, 0.313, 0.314, 0.316, 0.303, 0.316, 0.316, 0.291, 0.302, 0.321, 0.307, 0.312, 0.285, 0.322, 0.314, 0.316, 0.312, 0.317, 0.315, 0.317, 0.317, 0.318,
              0.316, 0.315, 0.321, 0.32, 0.308, 0.323, 0.316, 0.314, 0.303, 0.324, 0.316, 0.321, 0.315, 0.316, 0.32, 0.317, 0.318, 0.319, 0.317, 0.304, 0.328, 0.323, 0.313, 0.314, 0.318, 0.313, 0.321, 0.315, 0.325, 0.295, 0.32, 0.305,
              0.316, 0.312, 0.334, 0.323, 0.314, 0.315, 0.313, 0.321, 0.317, 0.317, 0.319, 0.317, 0.309, 0.315, 0.318, 0.317, 0.311, 0.337, 0.321, 0.318, 0.317, 0.315, 0.315, 0.336, 0.321, 0.317, 0.305, 0.314, 0.303, 0.314, 0.315, 0.301,
              0.323, 0.325, 0.309, 0.313, 0.314, 0.312, 0.314, 0.298, 0.319, 0.315, 0.31, 0.335, 0.315, 0.298, 0.304, 0.321, 0.32, 0.32, 0.313, 0.317, 0.304, 0.306, 0.315, 0.319, 0.316, 0.316, 0.317, 0.315, 0.328, 0.313, 0.317, 0.318,
              0.315, 0.331, 0.318, 0.295, 0.317, 0.312, 0.311, 0.312, 0.317, 0.316, 0.314, 0.315, 0.305, 0.315, 0.317, 0.316, 0.314, 0.313, 0.316, 0.326, 0.317, 0.296, 0.301, 0.309, 0.317, 0.309, 0.302, 0.302, 0.302, 0.316, 0.324, 0.323,
              0.325, 0.315, 0.318, 0.307, 0.314, 0.321, 0.31, 0.314, 0.288, 0.298, 0.327, 0.32, 0.297, 0.33, 0.313, 0.317, 0.318, 0.301, 0.317, 0.317, 0.303, 0.31, 0.314, 0.315, 0.314, 0.318, 0.315, 0.317, 0.32, 0.313, 0.315, 0.308, 0.318,
              0.31, 0.314, 0.31, 0.313, 0.317, 0.308, 0.277, 0.316, 0.319, 0.322, 0.327, 0.313, 0.313, 0.315, 0.314, 0.315, 0.314, 0.299, 0.311, 0.311, 0.299, 0.313, 0.317, 0.314, 0.315, 0.316, 0.314, 0.315, 0.314, 0.315, 0.315, 0.317,
              0.312, 0.317, 0.316, 0.314, 0.314, 0.316, 0.318, 0.314, 0.304, 0.316, 0.316, 0.315, 0.313, 0.316, 0.316, 0.319, 0.321, 0.316, 0.316, 0.317, 0.317, 0.316, 0.317, 0.316, 0.315, 0.314, 0.315, 0.315, 0.317, 0.314, 0.318, 0.327,
              0.315, 0.313, 0.314, 0.319, 0.312, 0.315, 0.315, 0.31, 0.31, 0.307, 0.323, 0.313, 0.317, 0.314, 0.315, 0.305, 0.316, 0.314, 0.311, 0.316, 0.313, 0.312, 0.313, 0.314, 0.323, 0.32, 0.316, 0.31, 0.316, 0.316, 0.314, 0.313, 0.315,
              0.314, 0.311, 0.293, 0.334, 0.315, 0.324, 0.315, 0.318, 0.317, 0.314, 0.314, 0.315, 0.319, 0.329, 0.308, 0.308, 0.316, 0.32, 0.309, 0.316, 0.317, 0.315, 0.316, 0.314, 0.314
            ],
            smooth: true,
            lineStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '一号泵频率',
            type: 'line',
            yAxisIndex: 1,
            data: [
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 37.7, 43.7, 43.7, 43.7, 43.7, 43.7,
              43.7, 43.7, 43.7, 43.9, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.9, 43.7, 43.7, 44.1, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.8, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.8,
              43.7, 43.7, 43.9, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.8, 43.8, 43.8, 43.7, 43.8, 43.7, 43.8, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.8, 43.7,
              43.7, 43.7, 43.7, 43.7, 43.8, 43.7, 43.7, 43.8, 43.7, 43.7, 43.7, 43.7, 43.7, 43.9, 43.7, 43.9, 43.7, 43.7, 43.7, 43.7, 44, 43.9, 43.7, 43.7, 43.7, 43.7, 43.9, 43.9, 43.9, 43.7, 43.7, 43.7, 43.7, 43.9, 43.7, 43.7, 43.9, 43.9,
              44, 43.9, 43.8, 43.9, 43.9, 43.8, 43.9, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.9, 43.9, 43.7, 43.7, 44, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.8, 43.9, 44, 43.9, 43.7, 43.7, 43.7, 43.9, 43.7, 43.9, 43.9, 43.8, 43.8, 43.8,
              43.9, 43.7, 43.8, 43.8, 43.7, 43.7, 43.8, 44.2, 44.2, 44.1, 44, 44, 44, 43.9, 43.9, 43.8, 43.9, 44.1, 44.3, 44.3, 44.5, 43.9, 44, 43.9, 43.8, 43.9, 43.9, 44.1, 44, 44.1, 44, 44.1, 43.9, 43.7, 43.9, 44, 43.9, 43.9, 43.7, 43.9,
              44, 44, 43.8, 44, 44.4, 44.1, 44.1, 43.8, 43.9, 43.8, 44, 43.9, 44.2, 44.4, 44, 44.1, 44.1, 43.8, 43.8, 44.3, 43.9, 43.9, 44, 43.9, 44, 43.8, 43.8, 44.1, 43.9, 44.6, 44.3, 44.5, 44.2, 44.1, 43.8, 44.1, 43.8, 44.1, 44.1, 43.9,
              43.9, 43.8, 44.1, 43.9, 43.8, 43.7, 44.2, 44.1, 43.9, 43.9, 43.6, 44.2, 44.2, 44, 44.4, 44.8, 44.1, 43.8, 43.9, 43.9, 44.4, 44.4, 44, 43.8, 43.8, 43.9, 43.8, 43.9, 43.8, 43.9, 44.1, 44.1, 44, 44.1, 44.4, 44.3, 44, 44, 44.3,
              44, 44.5, 44.4, 44.3, 44.3, 44, 44, 43.9, 43.9, 44, 43.8, 43.9, 43.8, 43.9, 44.2, 44, 43.9, 43.9, 43.7, 43.8, 43.9, 44, 44.1, 44.1, 44.9, 44.1, 44, 43.9, 44, 44.1, 44, 44.2, 43.7, 43.9, 44.1, 43.7, 44, 44, 44.7, 44, 44.3,
              44.4, 44.7, 44.5, 44, 43.9, 44, 44.7, 44, 43.9, 43.9, 44, 44.4, 44.5, 44, 44, 44.9, 44, 43.9, 44, 44.2, 43.9, 44.2, 44, 44, 44.2, 44.3, 44.2, 44.2, 44.1, 44.1, 44.1, 44, 44.5, 44.4, 44, 44, 43.9, 44.1, 43.9, 44.3, 43.9, 43.9,
              43.8, 44, 43.9, 43.8, 43.9, 44, 43.9, 43.9, 44, 44.1, 43.9, 43.8, 43.9, 43.8, 43.8, 44, 43.9, 44, 44.2, 44.6, 43.9, 43.8, 43.8, 44, 44.1, 44.9, 44.2, 44.2, 43.8, 43.8, 44, 43.9, 44.5, 43.8, 44, 44, 44, 44.2, 44.7, 44.2, 44,
              44.1, 44.1, 44.3, 44.1, 44.3, 44.2, 44.6, 44, 44.6, 44, 44.1, 44, 44.2, 44.2, 43.8, 43.9, 43.8, 44.2, 43.8, 43.7, 44.1, 43.8, 43.8, 43.8, 43.7, 44.4, 44.2, 44.1, 44, 43.9, 44, 43.9, 43.9, 43.8, 43.9, 44.1, 44, 43.8, 43.9, 44,
              44, 44, 43.9, 43.9, 43.8, 43.8, 44.1, 43.8, 44.1, 43.9, 44.1, 44, 44, 44, 43.9, 44.1, 44, 44.3, 43.9, 44.1, 44.5, 44.3, 44.9, 44.8, 44.4, 44.1, 44.1, 44, 44, 44.3, 44.3, 43.9, 43.9, 44.2, 44.6, 44.5, 44.1, 44.1, 44, 44.1,
              44.1, 44.3, 44.2, 43.8, 43.7, 43.8, 43.7, 43.8, 43.8, 44, 43.8, 43.9, 43.7, 43.8, 43.6, 43.6, 43.9, 43.8, 43.8, 43.8, 43.8, 43.6, 43.8, 43.9, 43.9, 43.7, 44.2, 44.3, 44.6, 44, 44.1, 43.7, 43.8, 43.9, 44, 43.8, 43.7, 43.8,
              43.9, 43.9, 43.8, 43.8, 44.1, 43.8, 43.7, 44, 44.3, 44, 44.2, 44.1, 44.3, 44.5, 44.4, 44, 44.1, 43.8, 44.1, 43.9, 43.9, 44, 44.3, 44, 43.9, 43.9, 44.1, 44.2, 44.8, 44, 43.8, 43.9, 44.1, 44, 44, 43.9, 43.9, 44.1, 44, 43.9,
              43.9, 44.2, 44.3, 44.4, 44.2, 44.2, 43.9, 44, 43.8, 43.7, 44, 44, 44, 44.2, 43.9, 43.7, 43.8, 43.8, 43.8, 43.9, 43.9, 44, 43.9, 44, 44.2, 44.3, 43.9, 43.8, 44.1, 43.8, 44, 44.1, 44, 43.9, 43.9, 43.9, 43.8, 44, 43.9, 43.8, 44,
              44, 44.3, 43.7, 43.8, 43.9, 43.7, 43.8, 44.1, 43.8, 43.9, 44.2, 43.9, 44, 43.8, 43.8, 43.8, 43.8, 43.8, 43.9, 43.7, 43.9, 43.9, 43.9, 43.7, 44, 43.8, 43.9, 43.8, 43.9, 43.9, 44.2, 43.8, 43.8, 44.1, 43.8, 44.2, 43.8, 43.8,
              43.9, 44, 43.7, 43.7, 43.6, 43.6, 43.6, 43.4, 43.6, 43.7, 43.9, 43.7, 43.8, 43.8, 43.7, 43.8, 43.8, 44.2, 43.9, 43.7, 43.6, 43.8, 43.6, 43.8, 43.8, 43.8, 43.9, 43.8, 43.8, 43.8, 43.8, 44, 43.9, 43.9, 43.9, 43.8, 43.8, 44,
              43.9, 43.8, 43.8, 43.8, 43.6, 43.7, 43.8, 43.7, 43.8, 43.8, 43.6, 43.9, 43.7, 43.7, 43.6, 43.6, 43.7, 43.6, 43.7, 43.7, 43.8, 43.9, 43.8, 44, 43.8, 43.8, 43.7, 43.8, 43.9, 43.6, 43.7, 43.7, 43.7, 43.7, 44, 43.9, 43.8, 43.6,
              43.6, 43.6, 43.7, 43.7, 43.6, 43.8, 43.9, 43.8, 43.7, 44.1, 43.8, 43.7, 44, 43.6, 43.7, 43.6, 43.8, 43.7, 43.8, 43.6, 43.8, 43.8, 43.7, 43.8, 43.8, 43.8, 43.9, 43.8, 43.8, 43.7, 43.8, 43.9, 43.7, 43.8, 43.8, 43.7, 43.7, 43.8,
              43.8, 43.8, 43.8, 43.8, 43.8, 43.7, 43.9, 43.7, 43.7, 43.7, 43.8, 44, 43.9, 43.8, 43.8, 43.7, 43.9, 43.8, 43.8, 43.8, 43.7, 44.1, 44, 43.9, 43.9, 44.1, 43.9, 43.8, 43.9, 43.8, 43.9, 43.8, 43.9, 44, 43.9, 43.8, 44.1, 43.8,
              43.9, 44, 43.9, 43.9, 44, 43.9, 43.8, 44, 43.9, 43.8, 43.9, 43.7, 43.8, 43.9, 43.9, 43.7, 43.9, 44, 43.9, 43.9, 43.9, 44.1, 43.9, 43.8, 43.9, 43.7, 43.8, 43.7, 44, 43.9, 43.7, 43.9, 43.9, 43.9, 44, 44.1, 44.2, 43.9, 44.4,
              44.3, 44.2, 43.9, 43.9, 43.9, 43.8, 44.2, 43.9, 43.9, 43.8, 43.9, 44, 44, 44, 43.9, 43.7, 44.2, 43.9, 44, 43.9, 43.8, 43.9, 44, 44.1, 44.4, 44, 44.1, 44.9, 44.5, 43.9, 43.9, 44, 44.4, 43.6, 43.9, 44, 43.6, 43.8, 44.4, 43.7,
              44, 43.8, 44, 43.8, 44.1, 43.8, 43.9, 43.9, 43.8, 44.2, 43.9, 43.8, 43.8, 43.8, 44, 43.7, 43.7, 43.8, 43.6, 43.7, 43.8, 43.8, 43.8, 43.7, 44.1, 44.3, 43.8, 43.8, 43.8, 43.8, 43.6, 43.9, 44.6, 43, 43.8, 43.8, 43.8, 43.8, 43.7,
              43.9, 43.8, 43.7, 43.5, 43.9, 43.8, 43.8, 43.8, 43.8, 43.8, 43.8, 43.6, 43.9, 43.9, 43.8, 44, 43.8, 43.9, 43.8, 43.9, 43.8, 43.9, 43.9, 43.7, 43.8, 44, 43.9, 44, 43.8, 44.1, 43.8, 43.9, 43.8, 43.7, 43.7, 43.7, 44.1, 43.8,
              43.7, 43.8, 44.3, 44.1, 44, 43.9, 43.9, 44, 43.9, 43.8, 44.1, 44, 44.1, 43.9, 44.1, 44.2, 44.1, 44, 44.7, 44, 44.3, 44.2, 44.8, 43.9, 44.2, 43.8, 44.2, 43.9, 44, 44.2, 43.9, 44.1, 44.4, 44.1, 44.2, 43.9, 43.8, 43.8, 43.8,
              43.9, 43.9, 43.9, 44.1, 44, 44, 44, 44.2, 44, 44.2, 44.2, 44.2, 44.1, 44, 44.1, 43.9, 43.9, 43.9, 44, 44.1, 44, 44, 44, 44.2, 44.4, 44.4, 44, 44.5, 43.9, 43.9, 44, 43.9, 43.8, 43.8, 43.8, 43.7, 43.9, 43.9, 43.9, 43.9, 44,
              44.6, 44.1, 44.1, 44.1, 43.8, 44.1, 44.2, 44.6, 44.4, 44, 44.4, 44.1, 44.5, 44.3, 44.3, 44, 43.8, 43.8, 43.9, 44.2, 44, 44.1, 44.6, 44.2, 44.3, 44.2, 44.9, 44.3, 44.5, 44.5, 45.2, 46, 45.1, 44.6, 44.6, 44.8, 44.5, 44.8, 44.5,
              45.2, 44.3, 45, 45, 45, 44.7, 44.5, 44.2, 44.2, 44.5, 44.4, 44.6, 44.5, 44.3, 43.9, 44, 43.9, 44, 43.9, 44.3, 43.9, 43.7, 44.2, 44.6, 44.8, 44.8, 43.9, 44, 44.1, 44.1, 44.2, 44.4, 44.6, 45, 44.9, 44.9, 43.9, 44.6, 44, 43.8,
              44.3, 44, 44.2, 44.1, 43.9, 44.8, 44.2, 44.2, 44, 44, 43.9, 43.9, 43.9, 43.9, 44, 44.6, 44.5, 44.7, 44.2, 44, 44, 44.1, 43.8, 44, 43.9, 43.8, 43.9, 43.9, 43.8, 43.8, 43.8, 43.8, 43.9, 43.9, 43.8, 43.8, 43.8, 43.9, 43.8, 43.8,
              43.7, 44, 44.1, 44.2, 44.2, 44.3, 43.9, 43.9, 44, 44, 44.4, 44.3, 43.8, 43.9, 44.1, 44.5, 44.4, 43.6, 43.8, 43.8, 43.8, 43.8, 44, 44.5, 44.3, 44.2, 43.9, 43.9, 44, 44, 43.8, 43.9, 44, 44.5, 44.9, 44.5, 45.5, 44.3, 44.3, 44.3,
              43.9, 44, 44.4, 44.2, 44.4, 44, 44, 44, 44.1, 43.9, 44, 43.9, 43.9, 44, 44, 44.3, 44, 43.8, 43.7, 43.7, 43.8, 43.9, 44, 43.9, 44.1, 43.8, 43.9, 43.9, 43.9, 43.9, 44, 44, 43.5, 43.9, 44, 43.9, 43.8, 43.7, 43.7, 43.7, 43.7,
              43.8, 43.8, 43.9
            ],
            smooth: true,
            lineStyle: {
              color: '#fac858'
            }
          },
          {
            name: '二号泵频率',
            type: 'line',
            yAxisIndex: 1,
            data: [
              43.6, 43.6, 43.6, 43.7, 43.6, 43.7, 43.8, 44.1, 43.7, 43.8, 43.9, 43.8, 43.8, 44.1, 43.9, 43.9, 44, 44.2, 43.7, 43.8, 43.7, 43.8, 43.9, 43.8, 43.6, 43.6, 43.8, 43.8, 43.8, 43.8, 43.7, 43.7, 43.7, 43.7, 43.9, 44, 44, 43.8,
              43.9, 44.1, 43.7, 43.7, 43.7, 43.8, 43.7, 43.8, 43.6, 43.6, 43.6, 43.6, 43.8, 43.6, 43.7, 43.7, 43.6, 43.6, 43.7, 43.6, 43.7, 43.8, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.7, 43.8, 43.8, 43.9, 43.8, 43.8, 43.7, 43.8, 43.8,
              43.8, 43.7, 43.8, 43.6, 43.6, 43.6, 43.6, 43.7, 43.6, 43.8, 43.8, 44.1, 43.8, 43.8, 43.8, 43.8, 43.7, 43.6, 43.6, 43.6, 43.5, 43.6, 43.6, 43.6, 43.8, 43.7, 43.6, 43.9, 43.7, 43.7, 43.6, 43.6, 43.6, 43.6, 43.6, 43.7, 43.8,
              43.9, 43.8, 43.8, 43.8, 43.8, 43.8, 44, 43.8, 44, 43.8, 43.8, 43.8, 43.8, 43.6, 43.6, 43.8, 43.7, 43.6, 43.6, 43.6, 43.7, 43.8, 43.7, 43.7, 43.7, 43.7, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.7, 43.7, 43.6, 43.6,
              43.9, 43.6, 43.6, 43.6, 43.6, 43.8, 43.7, 43.6, 43.6, 43.6, 43.6, 43.7, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.7, 43.7, 43.8, 43.7, 43.8, 43.8, 43.8, 43.8, 43.8, 43.8, 43.8, 43.9, 43.8, 43.7, 43.7, 43.7, 43.8, 43.7, 43.7,
              43.8, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.8, 43.8, 43.8, 43.8, 43.6, 43.6, 43.6, 43.6, 43.6, 43.6, 43.7, 43.6, 43.6, 43.6, 43.8, 43.6, 43.6, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
              0, 0, 0
            ],
            smooth: true,
            lineStyle: {
              color: '#ef0c2e'
            }
          },
          {
            type: 'bar',
            name: '用水量',
            yAxisIndex: 2,

            data: [
              '--',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.02',
              '0.11',
              '0.12',
              '0.13',
              '0.10',
              '0.11',
              '0.11',
              '0.14',
              '0.11',
              '0.11',
              '0.11',
              '0.02',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.04',
              '0.13',
              '0.11',
              '0.12',
              '0.11',
              '0.11',
              '0.13',
              '0.09',
              '0.14',
              '0.11',
              '0.13',
              '0.04',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.08',
              '0.13',
              '0.11',
              '0.11',
              '0.12',
              '0.13',
              '0.10',
              '0.13',
              '0.11',
              '0.12',
              '0.11',
              '0.02',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.09',
              '0.11',
              '0.13',
              '0.12',
              '0.13',
              '0.11',
              '0.11',
              '0.12',
              '0.11',
              '0.09',
              '0.07',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.04',
              '0.13',
              '0.12',
              '0.11',
              '0.13',
              '0.11',
              '0.11',
              '0.09',
              '0.14',
              '0.11',
              '0.09',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.02',
              '0.11',
              '0.09',
              '0.14',
              '0.11',
              '0.13',
              '0.11',
              '0.12',
              '0.13',
              '0.11',
              '0.11',
              '0.07',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.11',
              '0.11',
              '0.10',
              '0.14',
              '0.11',
              '0.12',
              '0.11',
              '0.13',
              '0.11',
              '0.12',
              '0.11',
              '0.05',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00',
              '0.00'
            ]
          }
        ]
      }

      myChart.setOption(option)

      // 响应式调整
      window.addEventListener('resize', function () {
        myChart.resize()
      })
    </script>
  </body>
</html>
